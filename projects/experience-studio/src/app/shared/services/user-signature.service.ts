import { Injectable , inject  } from '@angular/core';
// import { AuthService } from '../../services/auth.service';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class UserSignatureService {
  private readonly fallbackEmail = '<EMAIL>';
  // public authService = inject(AuthService);

  constructor() {
    // Subscribe to userProfile$ to keep the latest profile updated
    // this.authService.userProfile$.subscribe(profile => {
    //   this.latestUserProfile = profile;
    // }); 
  }

  /**
   * Gets the user's email from MSAL authentication
   * Falls back to a default email if the user is not authenticated
   * @returns An Observable with the user's email
   */
  // getUserSignature(): Observable<string> {
  //   return this.authService.userProfile$.pipe(
  //     map(profile => {
  //       if (profile && profile.mail) {

  //         return profile.mail;
  //       } else if (profile && profile.userPrincipalName) {
  //         return profile.userPrincipalName;
  //       } else {
  //         return this.fallbackEmail;
  //       }
  //     }),
  //     catchError(error => {
  //       return of(this.fallbackEmail);
  //     })
  //   );
  // }
  getUserSignature(): Observable<string> {
    return of(this.fallbackEmail);
  }


  // Store the latest user profile
  // private latestUserProfile: any = null;


  /**
   * Gets the user's email synchronously (with fallback)
   * This should only be used when an Observable is not practical
   * @returns The user's email or fallback email
   */
  // getUserSignatureSync(): string {
  //   // Use the stored profile value
  //   const profile = this.latestUserProfile;

  //   if (profile && profile.mail) {
  //     return profile.mail;
  //   } else if (profile && profile.userPrincipalName) {
  //     return profile.userPrincipalName;
  //   } else {
  //     return this.fallbackEmail;
  //   }
  // }
  getUserSignatureSync(): string {
    return this.fallbackEmail;
    }
}
