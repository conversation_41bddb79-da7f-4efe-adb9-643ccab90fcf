import { Injectable, inject } from '@angular/core';
import {
  Http<PERSON><PERSON>,
  Http<PERSON><PERSON><PERSON>,
  HttpInterceptor,
  HttpRequest,
  HttpErrorResponse,
} from '@angular/common/http';
import { Observable, throwError, BehaviorSubject } from 'rxjs';
import { catchError, filter, switchMap, take } from 'rxjs/operators';
import { TokenStorageService } from '../token-storage/token-storage.service';
import { AuthService } from '../auth-service/auth.service';

@Injectable()
export class InterceptorService implements HttpInterceptor {
  private tokenStorageService = inject(TokenStorageService);
  private authService = inject(AuthService);
  private isRefreshing = false;
  private refreshTokenSubject = new BehaviorSubject<string | null>(null);

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    const accessToken = this.tokenStorageService.getAccessToken();

    const authReq = accessToken ? this.addTokenHeader(req, accessToken) : req;

    return next.handle(authReq).pipe(catchError(error => this.handleError(error, authReq, next)));
  }

  private addTokenHeader(request: HttpRequest<any>, token: string): HttpRequest<any> {
    return request.clone({
      headers: request.headers.set('access-key', token.trim()),
    });
  }

  private handleError(
    error: any,
    request: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    if (error instanceof HttpErrorResponse && error.status === 401) {
      return this.handle401Error(request, next);
    }
    return throwError(() => error);
  }

  private handle401Error(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    if (!this.isRefreshing) {
      this.isRefreshing = true;
      this.refreshTokenSubject.next(null);

      return this.authService.refreshToken().pipe(
        switchMap(tokenResponse => {
          this.isRefreshing = false;
          const newAccessToken = tokenResponse.accessToken;
          this.tokenStorageService.storeAccessToken(newAccessToken);
          this.refreshTokenSubject.next(newAccessToken);
          return next.handle(this.addTokenHeader(request, newAccessToken));
        }),
        catchError(err => {
          this.isRefreshing = false;
          return throwError(() => err);
        })
      );
    }

    return this.refreshTokenSubject.pipe(
      filter(token => token != null),
      take(1),
      switchMap(token => next.handle(this.addTokenHeader(request, token!)))
    );
  }
}
