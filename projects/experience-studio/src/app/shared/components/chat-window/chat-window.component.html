<div class="chat-wrapper" [ngClass]="theme">
  <div class="chat-messages" #chatScrollContainer>
    <ng-container *ngFor="let message of chatMessages">
      <!-- User Message Card with Markdown Support -->
      <awe-cards
        *ngIf="message.from === 'user'"
        variant="basic"
        size="small"
        class="user-card"
        [ngClass]="theme"
        [theme]="theme">
        <div content class="markdown-content">
          <!-- Display the image if it exists in the message -->
          <div class="selected-image" *ngIf="message.imageDataUri">
            <div class="image-preview" (click)="showImagePreview(message.imageDataUri, 'Selected image')">
              <img [src]="message.imageDataUri" alt="Selected image" class="thumbnail-image" />
            </div>
          </div>
          <markdown [data]="getSanitizedMessageText(message.text)"></markdown>
        </div>
      </awe-cards>

      <!-- AI Message Card with Markdown Support -->
      <awe-cards
        *ngIf="message.from === 'ai'"
        variant="basic"
        size="small"
        class="ai-card"
        [ngClass]="[theme, message.hasSteps ? 'stepper-card' : '']"
        [theme]="theme">
        <div content class="markdown-content">
          <!-- Regular AI message content -->
          <div *ngIf="!message.hasSteps">
            <markdown [data]="getSanitizedMessageText(message.text)"></markdown>
            <!-- Show loading dots when stepper is active but not yet added to this message -->
            <div *ngIf="showStepper && status !== 'COMPLETED'" class="loading-dots">
              <div class="dot"></div>
              <div class="dot"></div>
              <div class="dot"></div>
            </div>
          </div>

          <!-- Stepper content if this message has steps -->
          <div *ngIf="message.hasSteps" class="stepper-content">
            <div *ngIf="message.text" class="stepper-intro">
              <markdown [data]="getSanitizedMessageText(message.text)"></markdown>
            </div>
            <app-vertical-stepper
              [theme]="theme"
              [progress]="progress"
              [progressDescription]="progressDescription"
              [status]="status"
              [restartable]="false"
              (stepUpdated)="onStepUpdated($event)"
              (retryStep)="onRetryStep($event)">
            </app-vertical-stepper>
          </div>
        </div>
      </awe-cards>
    </ng-container>

    <!-- Stepper is now rendered inside AI message cards -->

    <!-- Spacer to ensure content doesn't get hidden behind prompt bar -->
    <div class="prompt-spacer"></div>
  </div>

  <awe-prompt-bar
    [theme]="theme"
    [defaultText]="defaultText"
    [leftIcons]="leftIcons"
    [rightIcons]="rightIcons"
    [(textValue)]="textValue"
    (iconClicked)="handleIconClick($event)"
    (enterPressed)="isCodeGenerationComplete ? enterPressed.emit() : null"
    [variant]="'chat-bot'"
    [class.disabled-prompt-bar]="!isCodeGenerationComplete">
  </awe-prompt-bar>
</div>

<!-- Image Preview Overlay -->
<div class="preview-overlay" *ngIf="showPreview && previewImage">
  <div class="preview-content">
    <div class="preview-header">
      <div class="preview-title">{{ previewImage.name }}</div>
      <awe-icons
        iconName="awe_close"
        (click)="closeImagePreview()"
        role="button"
        tabindex="0"
        [attr.aria-label]="'Close preview'"
        [color]="getIconColor()"></awe-icons>
    </div>
    <div class="preview-body">
      <img [src]="previewImage.url" [alt]="previewImage.name" />
    </div>
  </div>
</div>
