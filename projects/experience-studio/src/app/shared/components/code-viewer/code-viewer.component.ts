import { Component, ElementRef, Input, OnInit, On<PERSON>estroy, ViewChild, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject, takeUntil } from 'rxjs';
import { FormsModule } from '@angular/forms';
import { ThemeService } from '../../services/theme-service/theme.service';
import { MonacoEditorService } from '../../services/monaco-editor/monaco-editor.service';

export interface FileModel {
  name: string;
  type: 'file' | 'folder';
  content?: string;
  children?: FileModel[];
  expanded?: boolean;
  fileName?: string;
}

// Interface for file input format
export interface FileInput {
  fileName: string;
  content: string;
}

@Component({
  selector: 'app-code-viewer',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './code-viewer.component.html',
  styleUrl: './code-viewer.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class CodeViewerComponent implements OnInit, On<PERSON><PERSON>roy {
  @ViewChild('editorContainer', { static: true }) editorContainer!: ElementRef;
  @Input() showFileExplorer = true;

  private destroy$ = new Subject<void>();
  currentTheme: 'light' | 'dark';

  // Updated to handle multiple active files
  activeFiles: FileModel[] = [];
  activeFile: FileModel | null = null;
  selectedFilePath: string | null = null; // Track the selected file path for hover effect
  isLoading: boolean = true;
  searchQuery: string = '';
  showWelcomeMessage: boolean = true;
  private maxTabsVisible = 10;

  // Updated setter to handle the new file format
  @Input() set files(value: FileModel[] | FileInput[]) {
    this.isLoading = true;
    this.showWelcomeMessage = !value || (Array.isArray(value) && value.length === 0);

    if (!value || !Array.isArray(value) || value.length === 0) {
      this._originalFiles = [];
      this._files = [];
      this.isLoading = false;
      return;
    }

    // Check if the input follows FileModel[] format, FileInput[] format, or FileData format
    const firstItem = value[0];
    let fileModels: FileModel[] = [];

    if ('type' in firstItem && firstItem.type === 'file') {
      // Already FileModel format
      fileModels = value as FileModel[];
    } else if ('fileName' in firstItem && 'content' in firstItem) {
      // FileInput format
      fileModels = this.transformFilesToFileModels(value as FileInput[]);
    } else if ('path' in firstItem && 'code' in firstItem) {
      // FileData format from polling service
      fileModels = this.transformFileDataToFileModels(value as any[]);
    } else {
      // Try to handle as FileInput format as fallback
      fileModels = this.transformFilesToFileModels(value as FileInput[]);
    }

    this._originalFiles = this.buildFileTree(fileModels);
    this._files = [...this._originalFiles];

    // Try to select the first file, but only if the editor is ready
    this.selectFirstFileIfAvailable();

    this.isLoading = false;
  }

  get files(): FileModel[] {
    return this._files;
  }

  @Input() theme: 'light' | 'dark' = 'dark'; // The theme input property
  private editor: any = null;
  private editorModelMap: Map<string, any> = new Map();
  private _files: FileModel[] = [];
  private _originalFiles: FileModel[] = [];

  constructor(
    private themeService: ThemeService,
    private monacoEditorService: MonacoEditorService,
    private cdr: ChangeDetectorRef
  ) {
    this.currentTheme = this.themeService.getCurrentTheme();
  }

  ngOnInit(): void {
    // Subscribe to theme changes
    this.themeService.themeObservable.pipe(takeUntil(this.destroy$)).subscribe(theme => {
      this.currentTheme = theme;
      this.applyTheme();
      this.cdr.markForCheck();
    });

    this.initializeEditor();
  }

  /**
   * Selects the first file in the file list if both the editor is ready and files are available
   */
  private selectFirstFileIfAvailable(): void {
    // Only proceed if both the editor is initialized and we have files
    if (this.editor && this._files.length > 0) {
      // Find the first file (not folder) in the file list
      const firstFile = this.findFirstFile(this._files);
      if (firstFile) {
        this.selectFile(firstFile);
        this.showWelcomeMessage = false;
      }
    }
  }

  /**
   * Recursively finds the first file (not folder) in the file tree
   */
  private findFirstFile(files: FileModel[]): FileModel | null {
    if (!files || files.length === 0) return null;

    // Look for the first file in the current level
    const firstFile = files.find(f => f.type === 'file');
    if (firstFile) return firstFile;

    // If no file at this level, recursively check folders
    for (const folder of files.filter(
      f => f.type === 'folder' && f.children && f.children.length > 0
    )) {
      const fileInFolder = this.findFirstFile(folder.children || []);
      if (fileInFolder) return fileInFolder;
    }

    return null;
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();

    // Dispose editor and models using the service
    this.monacoEditorService.disposeEditor('code-viewer');

    // Clean up any models we created
    this.editorModelMap.forEach((_, key) => {
      this.monacoEditorService.disposeModel(key);
    });
    this.editorModelMap.clear();
  }

  ngOnChanges(): void {
    this.applyTheme(); // Reapply theme when it changes
  }

  private initializeEditor(): void {
    if (!this.editorContainer) {
      return;
    }

    // Set loading state
    this.isLoading = true;

    // Create editor with the MonacoEditorService
    this.monacoEditorService
      .createEditor(
        this.editorContainer.nativeElement,
        {
          theme: this.currentTheme === 'dark' ? 'dark-theme' : 'light-theme',
          language: 'plaintext',
          automaticLayout: true,
          minimap: { enabled: false },
          scrollBeyondLastLine: false,
          readOnly: true,
          fontSize: 14,
          tabSize: 2,
          lineHeight: 22,
          stickyScroll: { enabled: false },
        },
        'code-viewer' // Unique ID for this editor instance
      )
      .subscribe(editor => {
        this.editor = editor;
        this.isLoading = false;
        this.applyTheme();

        // Select the first file when the editor is ready
        this.selectFirstFileIfAvailable();
      });
  }

  private applyTheme(): void {
    if (this.editor) {
      // Set the theme using the service
      this.monacoEditorService.setTheme(
        this.currentTheme === 'light' ? 'light-theme' : 'dark-theme'
      );
    }
  }

  private getFileLanguage(fileName: string): string {
    // Use the service to get the language for the file
    return this.monacoEditorService.getLanguageForFile(fileName);
  }

  // private getFileLanguage(fileName: string): string {
  //   const ext = fileName.split('.').pop()?.toLowerCase(); // Get the file extension

  //   // Ensure Monaco is initialized and the language configurations are loaded
  //   const languages = monaco.languages.getLanguages();

  //   // Check if any language has the current file extension
  //   const language = languages.find(lang => lang.extensions?.includes(`.${ext}`));

  //   // Return language id if found, or default to 'plaintext'
  //   return language ? language.id : 'plaintext';
  // }

  getFileIcon(fileName: string): string {
    if (!fileName) return 'awe_document';

    // Special case for tailwind.config.ts
    if (fileName === 'tailwind.config.ts' || fileName.endsWith('/tailwind.config.ts')) {
      return 'awe_react_ts';
    }

    // Handle special config files with multi-level extensions
    // if (fileName.endsWith('.config.ts') || fileName.endsWith('.config.js')) {
    //   return 'awe_config';
    // }

    if (fileName.endsWith('.d.ts')) {
      return 'awe_react_ic';
    }

    // React component files and patterns
    if (
      fileName.endsWith('.component.tsx') ||
      fileName.endsWith('.component.jsx') ||
      fileName.match(/\.[A-Z][a-zA-Z]*\.tsx$/) || // Matches patterns like Button.tsx, App.tsx
      fileName.match(/\.[A-Z][a-zA-Z]*\.jsx$/) || // Matches patterns like Button.jsx, App.jsx
      fileName.includes('React') ||
      fileName.includes('react')
    ) {
      return 'awe_react_ic';
    }

    // Angular component files
    if (fileName.endsWith('.component.ts')) {
      return 'awe_angular';
    }

    if (fileName.endsWith('.component.html')) {
      return 'awe_html';
    }

    if ( fileName.endsWith('.component.css')) {
      return 'awe_css';
    }
    if ( fileName.endsWith('.component.scss')) {
      return 'awe_scss';
    }

    if (fileName.endsWith('.module.ts')) {
      return 'awe_angular';
    }

    if (fileName.endsWith('.service.ts')) {
      return 'awe_react_ic';
    }
    if (fileName.endsWith('.config.ts') && !fileName.endsWith('tailwind.config.ts')) {
      return 'awe_react_ic';
    }

    // For regular files, get the extension
    const ext = fileName.split('.').pop()?.toLowerCase();

    // Always use awe_react_ic icon for .jsx files
    if (ext === 'jsx') {
      return 'awe_react_ic';
    }

    // Map common extensions to their icons
    switch (ext) {
      case 'ts':
        return 'awe_react_ic';
      case 'tsx':
        return 'awe_react_ic';
      case 'js':
        return 'awe_js';
      case 'jsx':
        return 'awe_react_ic';
      case 'html':
        return 'awe_html';
      case 'css':
        return 'awe_css';
      case 'scss':
      case 'sass':
        return 'awe_scss';
      case 'json':
        return 'awe_json';
      case 'md':
        return 'awe_md';
      case 'svg':
        return 'awe_svg';
      case 'png':
      case 'jpg':
      case 'jpeg':
      case 'gif':
        return 'awe_image';
      case 'pdf':
        return 'awe_pdf';
      case 'gitignore':
      case 'gitattributes':
        return 'awe_git';
      case 'yml':
      case 'yaml':
        return 'awe_yaml';
      case 'xml':
        return 'awe_xml';
      default:
        // Try to use the extension as the icon name if available
        return ext ? `awe_${ext}` : 'awe_document';
    }
  }

  //  /**
  //  * Get the path to the file icon image
  //  * @param fileName The name of the file
  //  * @returns The path to the icon image
  //  */
  // getFileIconPath(fileName: string): string {
  //   const iconName = this.getFileIcon(fileName);
  //   return `/assets/icons/${iconName}.svg`;

  selectFile(file: FileModel): void {
    if (!this.editor) {
      return;
    }

    // Set the selected file path for hover effect
    this.selectedFilePath = file.name;

    // Check if file is already open
    const existingFileIndex = this.activeFiles.findIndex(f => f.name === file.name);
    if (existingFileIndex !== -1) {
      // If file is already open, just make it active
      this.setActiveFile(this.activeFiles[existingFileIndex]);
      return;
    }

    // Save current file content before switching
    if (this.activeFile && this.activeFile.type === 'file') {
      this.activeFile.content = this.editor.getValue();
    }

    // Add new file to active files
    this.activeFiles.push(file);
    this.setActiveFile(file);
    this.showWelcomeMessage = false;

    // Get or create model for the file
    let model = this.editorModelMap.get(file.name);
    if (!model && file.type === 'file') {
      const language = this.getFileLanguage(file.name);
      const content =
        typeof file.content === 'string' ? file.content : JSON.stringify(file.content, null, 2);
      this.monacoEditorService
        .createOrGetModel(content, language, `file:///${file.name}`)
        .subscribe(newModel => {
          model = newModel;
          this.editorModelMap.set(file.name, model);
          this.editor.setModel(model);
          this.cdr.markForCheck();
        });
    } else if (model) {
      this.editor.setModel(model);
    }

    this.cdr.markForCheck();
  }

  setActiveFile(file: FileModel): void {
    this.activeFile = file;
    const model = this.editorModelMap.get(file.name);
    if (model) {
      this.editor.setModel(model);
    }
    this.cdr.markForCheck();
  }

  closeTab(file: FileModel): void {
    const index = this.activeFiles.findIndex(f => f.name === file.name);
    if (index === -1) return;

    // Save file content before closing
    if (file.type === 'file' && this.activeFile === file) {
      file.content = this.editor?.getValue() || '';
    }

    // Remove the file from active files
    this.activeFiles.splice(index, 1);

    // Remove the model
    const modelUri = `file:///${file.name}`;
    this.monacoEditorService.disposeModel(modelUri);
    this.editorModelMap.delete(file.name);

    // If we're closing the active file, activate another file
    if (this.activeFile === file) {
      if (this.activeFiles.length > 0) {
        // Activate the previous file, or the next one if we closed the first file
        const newIndex = Math.min(index, this.activeFiles.length - 1);
        this.setActiveFile(this.activeFiles[newIndex]);
      } else {
        this.activeFile = null;
        this.editor?.setModel(null);
        this.showWelcomeMessage = true;
        this.cdr.markForCheck();
      }
    }

    this.cdr.markForCheck();
  }

  toggleFolder(folder: FileModel): void {
    folder.expanded = !folder.expanded;
    this.cdr.markForCheck();
  }

  // Updated method to transform your input format to FileModel array
  private transformFilesToFileModels(files: FileInput[]): FileModel[] {
    if (!files || !Array.isArray(files)) {
      return [];
    }

    return files.map(file => ({
      name: file.fileName, // Use fileName as the name
      type: 'file',
      content: file.content,
    }));
  }

  // Transform FileData format (from polling service) to FileModel array
  private transformFileDataToFileModels(files: any[]): FileModel[] {
    if (!files || !Array.isArray(files)) {
      return [];
    }

    return files.map(file => ({
      name: file.path || file.fileName || 'Unknown file', // Use path as the name
      type: 'file',
      content: file.code || file.content || '',
      fileName: file.path || file.fileName
    }));
  }

  private buildFileTree(files: FileModel[]): FileModel[] {
    const root: FileModel = { name: 'root', type: 'folder', children: [], expanded: true };

    if (!files || !Array.isArray(files)) {
      return [];
    }

    files.forEach(file => {
      // Skip files without a name property
      if (!file || typeof file.name !== 'string') {
        return;
      }

      // Normalize path separators to forward slashes
      const normalizedName = file.name.replace(/\\/g, '/');
      const pathParts = normalizedName.split('/');
      let currentFolder = root;

      pathParts.forEach((part, index) => {
        if (index === pathParts.length - 1) {
          // Pass full filename and content to file object
          currentFolder.children?.push({
            name: part,
            type: 'file',
            content: file.content,
            fileName: file.fileName || file.name,
          });
        } else {
          let nextFolder = currentFolder.children?.find(child => child.name === part);
          if (!nextFolder) {
            nextFolder = { name: part, type: 'folder', children: [], expanded: true }; // Set expanded to true by default
            currentFolder.children?.push(nextFolder);
          }
          currentFolder = nextFolder;
        }
      });
    });

    return root.children || [];
  }

  public filterFiles(): void {
    if (!this.searchQuery) {
      this._files = [...this._originalFiles];
      this.cdr.markForCheck();
      return;
    }

    const searchLower = this.searchQuery.toLowerCase();
    this._files = this.filterFileTree(this._originalFiles, searchLower);
    this.cdr.markForCheck();
  }

  private filterFileTree(files: FileModel[], searchQuery: string): FileModel[] {
    return files.filter(file => {
      if (file.type === 'folder' && file.children) {
        const matchingChildren = this.filterFileTree(file.children, searchQuery);
        if (matchingChildren.length > 0) {
          return true;
        }
      }
      return file.name.toLowerCase().includes(searchQuery);
    });
  }

  /**
   * Get the appropriate icon color based on file type
   * @param fileName The name of the file
   * @returns The icon color to use
   */
  getFileIconColor(fileName: string): string {
    if (!fileName) return 'neutralIcon';

    // Get file extension
    const ext = fileName.split('.').pop()?.toLowerCase();

    // Assign colors based on file types
    switch (ext) {
      case 'jsx':
      case 'tsx':
        return 'lightblue';

      case 'ts':
      case 'js':
        return 'action'; // TypeScript/JavaScript files in action color

      case 'css':
      case 'scss':
      case 'sass':
        return 'success'; // Styling files in success/green

      case 'json':
      case 'yml':
      case 'yaml':
      case 'xml':
        return 'warning'; // Config files in warning/yellow

      case 'md':
        return 'neutralIcon'; // Documentation in neutral

      case 'gitignore':
      case 'gitattributes':
        return 'danger'; // Git files in danger/red

      default:
        // Default color based on theme
        return this.theme === 'dark' ? 'whiteIcon' : 'neutralIcon';
    }
  }

  /**
   * Get the path to the icon SVG file based on the file name
   * @param fileName The name of the file
   * @returns The path to the icon SVG file
   */
  getFileIconPath(fileName: string): string {
    const iconName = this.getFileIcon(fileName);

    // Special case for tailwind.config.ts
    if (iconName === 'tailwind_config_ts') {
      return '/assets/icons/awe_react_ts.svg';
    }

    return `/assets/icons/${iconName}.svg`;
  }
}
