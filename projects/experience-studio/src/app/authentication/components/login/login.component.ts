import { Component, inject, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CallbackComponent } from "../callback/callback.component";
import { AuthService } from '../../auth-service/auth.service';
import { environment } from '../../../../environments/environment';

@Component({
  selector: 'app-login',
  template: `<app-callback></app-callback>`,
  imports: [CallbackComponent],
  standalone: true
})
export class LoginComponent implements OnInit {
  private authService = inject(AuthService);
  private route = inject(ActivatedRoute);
  private router = inject(Router);

  ngOnInit(): void {
    const redirectUrl = environment.redirectUrl;
    this.handleLogin(redirectUrl);
  }

  private handleLogin(redirectUrl: string): void {
    this.authService.login(redirectUrl).subscribe({
      next: () => {
        const code = this.route.snapshot.queryParams['code'];
        if (code) {
          this.handleTokenExchange(code, redirectUrl);
        }
      },
      error: (err) => {
        console.error('Login failed:', err);
      },
    });
  }

  private handleTokenExchange(code: string, redirectUrl: string): void {
    this.authService.exchangeCodeForToken(code, redirectUrl).subscribe({
      next: () => {
        console.log('Login successful, redirecting to experience...');
        this.router.navigate(['/experience/main']);
      },
      error: (err) => {
        console.error('Token exchange failed:', err);
        this.router.navigate(['/experience/main']);
      },
    });
  }
}
