import { CommonModule } from '@angular/common';
import { Component, Input, OnDestroy, OnInit, ChangeDetectorRef, ChangeDetectionStrategy } from '@angular/core';
import { Router } from '@angular/router';
import {
  ButtonComponent,
  PromptBarComponent,
  IconsComponent,
  FileAttachPillComponent,
  IconPillComponent,
  FileAttachOption,
} from '@awe/play-comp-library';
import { createLogger } from '../../../../shared/utils/logger'
import { Subscription } from 'rxjs';
import { HeroSectionHeaderComponent } from '../../../../shared/components/hero-section-header/hero-section-header.component';
import { CardDataService } from '../../../../shared/services/data-services/card-data.service';
import { CodeGenerationService } from '../../../../shared/services/code-generation.service';
import {
  CompleteSelections,
  PromptBarService,
} from '../../../../shared/services/prompt-bar-servises/prompt-bar.service';
import { ThemeService } from '../../../../shared/services/theme-service/theme.service';
import { PollingService } from '../../../../shared/services/polling.service';
import { AppStateService } from '../../../../shared/services/app-state.service';
import { SelectedFile, IconStatus, IconOption, Buttons } from '../../modals/image-to-code.modal';
import {
  buttonLabels,
  designOptions,
  fileOptions,
  promptContentConstants,
} from '../../constants/image-to-code.constant';
import { UserService } from '../../../../shared/services/user/user.service';
import { UserSignatureService } from '../../../../shared/services/user-signature.service';
import { PromptSubmissionService } from '../../../../shared/services/prompt-submission.service';
import { ToastService } from '../../../../shared/services/toast.service';
import { CardSelectionService } from '../../../../shared/services/card-selection.service';
import { TypewriterService } from '../../../../shared/services/typewriter.service';
import { TypewriterPlaceholderDirective } from '../../../../shared/directives/typewriter-placeholder.directive';
import { ServiceFactoryService } from '../../../../shared/services/service-factory.service';

@Component({
  selector: 'app-prompt-content',
  standalone: true,
  imports: [
    PromptBarComponent,
    ButtonComponent,
    CommonModule,
    HeroSectionHeaderComponent,
    FileAttachPillComponent,
    IconPillComponent,
    IconsComponent,
    TypewriterPlaceholderDirective,
  ],
  templateUrl: './prompt-content.component.html',
  styleUrl: './prompt-content.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PromptContentComponent implements OnInit, OnDestroy {
  currentPrompt = '';
  selectedCardTitle = 'Generate Application';
  theme: 'light' | 'dark' = 'light';
  animatedTexts = promptContentConstants.animatedTexts;
  selectedFiles: SelectedFile[] = [];
  selectedFile: File | null = null;
  selectedFileName = '';
  previewFile: SelectedFile | null = null;
  readonly maxAllowedFiles = 1;
  isFileAttachDisabled = false;
  fileError = '';
  isEnhancing = false;
  isGenerating = false;
  showPreview = false;
  isEnhancedPromptValid: boolean = true;
  isPromptEnhanced: boolean = false;
  enhanceClickCount = 0;
  maxEnhanceClicks = 2;
  isPolling = false;
  techOptions: IconOption[] = promptContentConstants.techOptions;
  selectedTech: IconOption = this.techOptions[0];
  designOptions: IconOption[] = designOptions;
  selectedDesign: IconOption = this.designOptions[0];
  leftIcons: { name: string; status: IconStatus; color: string }[] = [];
  rightIcons: { name: string; status: IconStatus; color: string }[] = [];
  fileOptions: FileAttachOption[] = fileOptions;
  @Input() buttons: Buttons[] = buttonLabels;
  jobId: string | null = null;
  projectId: string | undefined = undefined;
  submissionData = {
    prompt: this.currentPrompt,
    timestamp: new Date().toISOString(),
    imageFile: this.selectedFile,
    imageUrl: this.selectedFile ? URL.createObjectURL(this.selectedFile) : null,
    imageDataUri: null as string | null,
    fileName: this.selectedFileName || null,
  };
  private themeSubscription!: Subscription;
  private promptSubscription!: Subscription;
  private cardTitleSubscription: Subscription = new Subscription();
  private textareaObserver: MutationObserver | null = null;
  private boundHandlePasteEvent!: (event: ClipboardEvent) => void;
  private logger = createLogger('PromptContentComponent');

  constructor(
    private readonly themeService: ThemeService,
    public readonly promptService: PromptBarService,
    private readonly cardDataService: CardDataService,
    private readonly codeGenerationService: CodeGenerationService,
    private readonly router: Router,
    private readonly pollingService: PollingService,
    private readonly appStateService: AppStateService,
    private readonly cdr: ChangeDetectorRef,
    private readonly userService: UserService,
    private readonly userSignatureService: UserSignatureService,
    private readonly promptSubmissionService: PromptSubmissionService,
    private readonly toastService: ToastService,
    private readonly cardSelectionService: CardSelectionService,
    public readonly typewriterService: TypewriterService,
    private readonly serviceFactory: ServiceFactoryService
  ) {}

  ngOnInit(): void {
    this.resetComponentState();
    this.initTheme();
    this.initPromptData();
    this.initCardTitleSync();
    this.fetchUserIdOnLogin();
    this.promptSubmissionService.resetSubmissionState();
    this.initTypewriterEffect();

    // Verify that a card was selected from the landing page
    if (!this.cardSelectionService.hasCardBeenSelected()) {
      this.router.navigate(['/experience/main']);
    }
  }

  /**
   * Initialize the typewriter effect for the prompt bar placeholder
   */
  private initTypewriterEffect(): void {
    // Determine if we're showing image-related prompts
    const isImageUploaded = this.selectedFiles.length > 0;
    const staticText = isImageUploaded ? '' : 'Ask Studio to create';

    // Start the typewriter animation with the animated texts
    this.typewriterService.startTypewriter(
      this.animatedTexts.map(text => text.replace(/"/g, '')), // Remove quotes from the texts
      'Ask Studio to create', // Static text
      40, // Typing speed
      30, // Erasing speed
      400, // Pause before erasing
      200 // Pause before typing
    );
  }

  ngAfterViewInit(): void {
    this.boundHandlePasteEvent = this.handlePasteEvent.bind(this);
    document.addEventListener('paste', this.boundHandlePasteEvent);
    this.setupTextareaDisableObserver();
  }

  ngOnDestroy(): void {
    [this.themeSubscription, this.promptSubscription, this.cardTitleSubscription].forEach(sub =>
      sub?.unsubscribe()
    );
    this.cleanupDomListeners();
    this.pollingService.resetLogs();
    if (this.isPolling) {
      this.pollingService.stopPolling();
      this.isPolling = false;
    }

    // Stop the typewriter animation
    this.typewriterService.stopTypewriter();
  }

  handleEnterPressed(): void {
    if (this.isProcessing() || !this.currentPrompt?.trim()) {
      return;
    }
    this.handleEnhancedSend();
  }

  handleEnhancedSend(): void {
    if (this.isEnhancing || !this.currentPrompt?.trim()) return;
    if (this.isPromptEnhanced && !this.isEnhancedPromptValid) {
      return;
    }
    const prompt = this.currentPrompt.trim();
    const timestamp = new Date().toISOString();
    const file = this.selectedFile;
    const imageDataUri = this.submissionData.imageDataUri;
    const imageUrl = file ? URL.createObjectURL(file) : null;
    const fileName = this.selectedFileName || null;
    this.promptService.setPrompt(prompt);
    this.promptService.setSelectedCardTitle(this.selectedCardTitle);
    this.isGenerating = true;
    this.disablePromptBarElements(true);
    this.submissionData = {
      prompt,
      timestamp,
      imageFile: file,
      imageDataUri,
      imageUrl,
      fileName,
    };
    const initialData: CompleteSelections = {
      type: this.selectedCardTitle,
      prompt,
      application: 'web',
      technology: this.selectedTech?.value || 'angular',
      designLibrary: this.selectedDesign?.value || 'tailwindcss',
      imageUrl,
      imageDataUri,
    };
    this.appStateService.setCompleteSelections(initialData);
    this.promptService.setCompleteSelections(initialData);
    this.promptSubmissionService.setPromptSubmitted(true);
    // Determine the route based on the card title
    let routePrefix = '';
    if (this.selectedCardTitle === 'Generate UI Design') {
      routePrefix = '/experience/generate-ui-design';
    } else if (this.selectedCardTitle === 'Generate Application') {
      routePrefix = '/experience/generate-application';
    } else {
      // Fallback to legacy route
      routePrefix = '/experience';
    }

    const targetRoute = `${routePrefix}/code-preview`;

    // Reset the card selection state since we're now moving to code-preview
    this.cardSelectionService.resetSelectionState();

    this.router.navigateByUrl(targetRoute);
    this.toastService.success('Processing your request. Starting code generation...');
    this.handleSubmit();
  }

  handlePromptChange(event: Event): void {
    const input = event.target as HTMLInputElement;
    this.currentPrompt = input.value;
    this.promptService.setPrompt(this.currentPrompt);
    this.isEnhancedPromptValid = true;
    this.isPromptEnhanced = false;

    // Check if the prompt is now empty and we have an image uploaded
    if ((!this.currentPrompt || this.currentPrompt.trim() === '') && this.selectedFiles.length > 0) {
      // Update to image-specific prompts
      this.animatedTexts = promptContentConstants.imageUploadAnimatedTexts;
      this.initTypewriterEffect();
    } else if ((!this.currentPrompt || this.currentPrompt.trim() === '') && this.selectedFiles.length === 0) {
      // Update to default prompts if no image and empty prompt
      this.animatedTexts = promptContentConstants.animatedTexts;
      this.initTypewriterEffect();
    }

    this.adjustTextareaHeight();
    this.updateIconDisabledState();
    this.updateSendButtonState();
  }

  handleIconClick(event: { name: string; side: string; index: number }): void {
    const normalizedIconName = event.name.toLowerCase();
    switch (normalizedIconName) {
      case 'awe_enhanced_alternate':
        this.handleEnhancedAlternate();
        break;
      case 'awe_enhance':
        this.handleEnhanceText();
        break;
      case 'awe_enhanced_send':
        this.handleEnhancedSend();
        break;
      default:
        this.logger.warn('Unknown icon clicked:', event.name);
    }
  }

  onFileRemoved(fileId: string): void {
    this.removeFile(fileId);
    this.promptService.resetEnhancedPromptState();
    this.promptService.setImage(null);
    this.isPromptEnhanced = false;
    this.isEnhancedPromptValid = true;
    this.enhanceClickCount = 0;
    this.currentPrompt = '';
    this.updateSendButtonState();

    // Reset the animated text back to the default prompts
    this.animatedTexts = promptContentConstants.animatedTexts;
    // Restart the typewriter effect with the original texts
    this.initTypewriterEffect();

    this.toastService.info('Image removed. You can upload a new image or enter a prompt.');
  }

  closePreview(): void {
    this.previewFile = null;
    this.showPreview = false;
  }

  showFilePreview(file: SelectedFile): void {
    this.previewFile = file;
    this.showPreview = true;
  }

  truncateFileName(filename: string): string {
    const maxLength = 13;
    const dotIndex = filename.lastIndexOf('.');
    if (filename.length <= maxLength || dotIndex === -1) return filename;
    const extension = filename.slice(dotIndex);
    const nameWithoutExt = filename.slice(0, dotIndex);
    const truncatedName = nameWithoutExt.slice(0, maxLength - extension.length - 3);
    return `${truncatedName}...${extension}`;
  }

  removeFile(fileId: string): void {
    this.selectedFiles = this.selectedFiles.filter(file => file.id !== fileId);
    this.updateFileAttachPillStatus();
  }

  onFileOptionSelected(option: FileAttachOption): void {
    if (this.isProcessing() || this.isMaxFilesReached()) {
      this.fileError = `Only ${this.maxAllowedFiles} image can be uploaded at a time`;
      this.toastService.warning(this.fileError);
      return;
    }
    if (option.value === 'computer') {
      this.handleEnhancedAlternate();
      return;
    }
    if (option.value === 'url') {
      if (this.isEnhancing || this.isGenerating) return;
      const url = prompt('Enter the URL of the file:');
      if (!url) return;
      const mockFile: SelectedFile = {
        id: Math.random().toString(36).substring(2, 11),
        name: url.split('/').pop() || 'file.jpg',
        url,
        type: 'image/jpeg',
      };
      this.selectedFiles = [...this.selectedFiles, mockFile];
      this.updateFileAttachPillStatus();
      this.toastService.success('Image added successfully. You can now enhance your prompt.');
    }
  }

  onTechSelected(option: IconOption): void {
    if (option.disabled || this.isProcessing()) {
      return;
    }
    this.selectedTech = option;
    this.toastService.info(`Selected ${option.name} as the framework for code generation.`);
  }

  onDesignSelected(option: IconOption): void {
    if (option.disabled) return;
    this.selectedDesign = option;
    this.toastService.info(`Selected ${option.name} as the design library for code generation.`);
  }

  handleEnhanceText(): void {
    if (this.enhanceClickCount >= this.maxEnhanceClicks || !this.currentPrompt?.trim()) return;
    this.isEnhancing = true;
    this.rightIcons[0].status = 'disable';
    this.disablePromptBarElements(true);
    const imageDataUris =
      this.selectedFiles.length && this.submissionData.imageDataUri
        ? [this.submissionData.imageDataUri]
        : [];
    this.promptService
      .enhancePrompt(this.currentPrompt, 'Generate Application', imageDataUris)
      .subscribe({
        next: (response: any) => {
          let parsedResponse: any;
          try {
            parsedResponse = typeof response === 'string' ? JSON.parse(response) : response;
          } catch (error) {
            parsedResponse = response;
          }
          const { prompt, intent } = parsedResponse || {};
          if (!prompt) {
            this.enablePromptBar();
            return;
          }
          this.currentPrompt = prompt;
          this.promptService.setEnhancedPrompt(prompt);
          this.isPromptEnhanced = true;
          this.updateTextareaValue(prompt);
          const normalizedIntent = intent?.toLowerCase() || '';
          if (normalizedIntent === 'yes') {
            this.isEnhancedPromptValid = true;
          } else {
            this.isEnhancedPromptValid = false;
            if (this.enhanceClickCount < this.maxEnhanceClicks) {
              this.rightIcons[0].status = 'active';
            }
          }
          setTimeout(() => this.adjustTextareaHeight(), 0);
          if (normalizedIntent === 'yes') {
            this.enhanceClickCount++;
            if (this.enhanceClickCount >= this.maxEnhanceClicks) {
              this.rightIcons[0].status = 'disable';
            }
            this.toastService.success('Prompt enhanced successfully. Ready to generate code.');
          } else {
            if (this.enhanceClickCount < this.maxEnhanceClicks) {
              this.rightIcons[0].status = 'active';
            }
            this.toastService.warning('Prompt needs more details. Click enhance again for better results.');
          }
          this.updateSendButtonState();
        },
        error: () => {
          this.isEnhancedPromptValid = true;
          this.enablePromptBar();

          this.toastService.error('Unable to enhance prompt. Please try again with more specific details.');
        },
        complete: () => {
          if (this.enhanceClickCount < this.maxEnhanceClicks) {
            this.rightIcons[0].status = 'active';
          }
          this.enablePromptBar();
        },
      });
  }

  private updateSendButtonState(): void {
    const sendButtonIcon = this.rightIcons[1];
    if (sendButtonIcon) {
      if (this.isPromptEnhanced) {
        sendButtonIcon.status = this.isEnhancedPromptValid ? 'active' : 'disable';
      } else {
        sendButtonIcon.status = 'active';
      }
      const enhanceButtonIcon = this.rightIcons[0];
      if (enhanceButtonIcon) {
        enhanceButtonIcon.status =
          this.enhanceClickCount >= this.maxEnhanceClicks ? 'disable' : 'active';
        if (
          this.isPromptEnhanced &&
          !this.isEnhancedPromptValid &&
          this.enhanceClickCount < this.maxEnhanceClicks
        ) {
          enhanceButtonIcon.status = 'active';
        }
      }
    }
  }

  getIconColor(): string {
    const isDisabled =
      !this.currentPrompt?.trim() ||
      this.isEnhancing ||
      this.enhanceClickCount >= this.maxEnhanceClicks;
    return `var(--icon-${isDisabled ? 'disabled' : 'enabled'}-color)`;
  }

  handleSuggestionClick(suggestion: string): void {
    this.currentPrompt = suggestion.replace(/^✨\s*/, '').trim();
    this.promptService.setPrompt(this.currentPrompt);
    this.adjustTextareaHeight();
    this.updateIconDisabledState();
  }

  private initTheme(): void {
    this.theme = this.themeService.getCurrentTheme();
    this.updateIconColors();
    this.themeSubscription = this.themeService.themeObservable.subscribe((theme: any) => {
      this.theme = theme;
      this.updateIconColors();
    });
  }

  private initPromptData(): void {
    this.promptService.setSelectedCardTitle(this.selectedCardTitle);
    this.promptSubscription = this.promptService.promptData$.subscribe((data: any) => {
      this.currentPrompt = data.prompt;
      if (data.enhancedPrompt) {
        this.isPromptEnhanced = true;
        this.isEnhancedPromptValid = true;
      } else {
        this.isPromptEnhanced = false;
        this.isEnhancedPromptValid = true;
      }
      if (data.imageFile) {
        this.selectedFile = data.imageFile;
        this.selectedFileName = data.imageFile.name;
      } else {
        this.isPromptEnhanced = false;
        this.isEnhancedPromptValid = true;
        this.enhanceClickCount = 0;
      }
      setTimeout(() => {
        this.updateSendButtonState();
      }, 0);
    });
  }

  private initCardTitleSync(): void {
    this.cardTitleSubscription = this.cardDataService.selectedCardTitle$.subscribe((cardTitle) => {
      if (cardTitle) {
        this.selectedCardTitle = cardTitle;
        this.promptService.setSelectedCardTitle(this.selectedCardTitle);
      }
    });
  }

  private fetchUserIdOnLogin(): void {
    const userEmail = this.userSignatureService.getUserSignatureSync();
    const handleUserId = (userId: string) => {
      this.appStateService.setUserId(userId);
      this.userService.setUserId(userId);
    };
    this.userService.getUserIdByEmail(userEmail).subscribe({
      next: handleUserId,
      error: () => handleUserId('01df7a8f-8af7-477a-9e69-7d3a236fa774'),
    });
  }

  private handlePasteEvent(event: ClipboardEvent): void {
    if (this.isProcessing() || this.isMaxFilesReached()) {
      return;
    }
    const items = event.clipboardData?.items;
    if (!items) return;
    for (const item of items) {
      if (!item.type.includes('image')) continue;
      const file = item.getAsFile();
      if (!file || !this.validateFile(file)) continue;
      this.promptService.resetEnhancedPromptState();
      this.isPromptEnhanced = false;
      this.isEnhancedPromptValid = true;
      this.enhanceClickCount = 0;
      this.currentPrompt = '';
      this.promptService.setPrompt('');
      const fileUrl = URL.createObjectURL(file);
      const newFile: SelectedFile = {
        id: Math.random().toString(36).substring(2, 11),
        name: file.name || `pasted-${Date.now()}.png`,
        url: fileUrl,
        type: file.type,
      };
      this.selectedFile = file;
      this.selectedFileName = newFile.name;
      this.selectedFiles = [newFile];
      this.updateFileAttachPillStatus();
      const reader = new FileReader();
      reader.onload = e => this.handleFileReaderLoad(e, file, newFile, fileUrl);
      this.updateIconDisabledState();
      this.updateSendButtonState();

      // Update the animated text to image-specific prompts only if the prompt is empty
      if (!this.currentPrompt || this.currentPrompt.trim() === '') {
        this.animatedTexts = promptContentConstants.imageUploadAnimatedTexts;
        // Restart the typewriter effect with the new texts
        this.initTypewriterEffect();
      }

      reader.readAsDataURL(file);
      break;
    }
  }

  private handleFileReaderLoad(
    e: ProgressEvent<FileReader>,
    file: File,
    newFile: SelectedFile,
    fileUrl: string
  ): void {
    const imageDataUri = e.target?.result as string;
    this.submissionData = {
      ...this.submissionData,
      prompt: '', // Reset the prompt
      imageFile: file,
      imageUrl: fileUrl,
      imageDataUri,
      fileName: newFile.name,
    };
    this.promptService.resetEnhancedPromptState();
    this.isPromptEnhanced = false;
    this.isEnhancedPromptValid = true;
    this.enhanceClickCount = 0;
    this.currentPrompt = '';
    this.promptService.setPrompt('');
    this.promptService.setImage(file);
    this.updateSendButtonState();
  }

  private resetComponentState(): void {
    this.currentPrompt = '';
    this.selectedFiles = [];
    this.selectedFile = null;
    this.selectedFileName = '';
    this.previewFile = null;
    this.isFileAttachDisabled = false;
    this.fileError = '';
    this.isEnhancing = false;
    this.isGenerating = false;
    this.showPreview = false;
    this.enhanceClickCount = 0;
    this.isPolling = false;
    this.isEnhancedPromptValid = true;
    this.isPromptEnhanced = false;
    this.selectedTech = this.techOptions[0];
    this.selectedDesign = this.designOptions[0];
    // Reset animated texts to default
    this.animatedTexts = promptContentConstants.animatedTexts;
    this.submissionData = {
      prompt: '',
      timestamp: new Date().toISOString(),
      imageFile: null,
      imageUrl: null,
      imageDataUri: null,
      fileName: null,
    };
    this.promptService.setPrompt('');
    this.promptService.resetEnhancedPromptState();
    this.promptService.setImage(null);
  }

  private setupTextareaDisableObserver(): void {
    const observer = new MutationObserver(() => {
      if (this.isProcessing()) {
        const textareas = document.querySelectorAll('.awe-prompt-bar textarea');
        textareas.forEach(textarea => {
          const htmlTextarea = textarea as HTMLTextAreaElement;
          htmlTextarea.disabled = true;
          htmlTextarea.setAttribute('disabled', 'true');
          htmlTextarea.style.opacity = '0.7';
          htmlTextarea.style.cursor = 'not-allowed';
          htmlTextarea.style.pointerEvents = 'none';
          htmlTextarea.style.backgroundColor = 'transparent';
        });
      }
    });
    observer.observe(document.body, { childList: true, subtree: true });
    this.textareaObserver = observer;
  }

  private updateIconColors(): void {
    const iconColor = this.theme === 'dark' ? 'var(--header-icon)' : 'var(--header-icon-border)';
    this.leftIcons = [{ name: 'awe_enhanced_alternate', status: 'active', color: iconColor }];
    this.rightIcons = [
      { name: 'awe_enhance', status: 'active', color: iconColor },
      { name: 'awe_enhanced_send', status: 'active', color: iconColor },
    ];
  }

  private isProcessing(): boolean {
    return this.isEnhancing || this.isGenerating;
  }

  private isMaxFilesReached(): boolean {
    return this.selectedFiles.length >= this.maxAllowedFiles;
  }

  private validateFile(file: File): boolean {
    const acceptedImageTypes = promptContentConstants.acceptedImageTypes;
    if (!file.type) {
      this.fileError = 'Folders cannot be uploaded';
    } else if (!acceptedImageTypes.includes(file.type)) {
      this.fileError = 'Only image files (JPEG, PNG, GIF, WEBP, SVG) are allowed';
    } else if (file.size > 5 * 1024 * 1024) {
      this.fileError = 'File size must be less than 5MB';
    } else {
      this.fileError = '';
      return true;
    }
    return false;
  }

  private updateFileAttachPillStatus(): void {
    const isMaxReached = this.isMaxFilesReached();
    const uploadIcon = this.leftIcons.find(icon => icon.name === 'awe_enhanced_alternate');
    if (uploadIcon) {
      uploadIcon.status = isMaxReached ? 'disable' : 'default';
    }
    this.isFileAttachDisabled = isMaxReached;
  }

  private disablePromptBarElements(isProcessing: boolean): void {
    try {
      document.body.classList.toggle('prompt-bar-processing', isProcessing);

      // Add or remove the processing class to the prompt bar
      const promptBar = document.querySelector('.prompt-bar') as HTMLElement;
      if (promptBar) {
        promptBar.classList.toggle('processing', isProcessing);
      }

      const textArea = document.querySelector('.awe-prompt-bar textarea') as HTMLTextAreaElement;
      if (textArea) {
        textArea.disabled = isProcessing;
        textArea.setAttribute('disabled', String(isProcessing));
        const textAreaStyles = isProcessing
          ? {
              opacity: '0.7',
              cursor: 'not-allowed',
              pointerEvents: 'none',
              backgroundColor: 'transparent',
            }
          : {
              opacity: '1',
              cursor: 'text',
              pointerEvents: 'auto',
              backgroundColor: '',
            };
        this.setElementStyles(textArea, true, textAreaStyles);
        if (isProcessing) {
          setTimeout(() => {
            textArea.disabled = true;
            textArea.setAttribute('disabled', 'true');
          }, 0);
        }
      }
      document.querySelectorAll('.pills-container > *').forEach(pill => {
        const htmlPill = pill as HTMLElement;
        const pillStyles = {
          opacity: isProcessing ? '0.7' : '',
          pointerEvents: isProcessing ? 'none' : '',
          cursor: isProcessing ? 'not-allowed' : 'pointer',
          backgroundColor: isProcessing ? 'transparent' : '',
        };
        this.setElementStyles(htmlPill, true, pillStyles);
        this.toggleInteractiveElements(htmlPill, isProcessing);
        if (isProcessing) {
          htmlPill.querySelectorAll('*').forEach(child => {
            (child as HTMLElement).style.backgroundColor = 'transparent';
          });
        }
      });
      const iconGroups = [
        { selector: '.enhance-icons awe-icons', cursor: 'not-allowed' },
        { selector: '.file-item awe-icons', cursor: 'not-allowed' },
      ];
      iconGroups.forEach(({ selector, cursor }) => {
        document.querySelectorAll(selector).forEach(icon => {
          const htmlIcon = icon as HTMLElement;
          const iconStyles = {
            opacity: isProcessing ? '0.7' : '',
            pointerEvents: isProcessing ? 'none' : '',
            cursor: isProcessing ? cursor : 'pointer',
          };
          this.setElementStyles(htmlIcon, true, iconStyles);
        });
      });
    } catch (error) {
      this.logger.error('Error while trying to disable/enable prompt bar elements:', error);
    }
  }

  public handleSubmit(): void {
    const trimmedPrompt = this.currentPrompt?.trim();
    if (!trimmedPrompt) {
      return;
    }
    this.isGenerating = true;
    const projectData = {
      selectedCardTitle: this.selectedCardTitle,
      prompt: trimmedPrompt,
    };
    const userSignature = this.userSignatureService.getUserSignatureSync();
    this.codeGenerationService.createProject(projectData, userSignature).subscribe({
      next: () => this.handleProjectResponse(),
      error: () =>
        this.handleProjectCreationError(
          'An error occurred while creating the project. Please try again.'
        ),
    });
  }

  private handleProjectResponse(): void {
    const completeData = this.buildCompleteData({ includeImage: true, includeIds: true });
    this.appStateService.setCompleteSelections(completeData);
    this.promptService.setCompleteSelections(completeData);
    this.isGenerating = false;
    this.generateCodeWithProject();
  }

  private setElementStyles(
    el: HTMLElement,
    isProcessing: boolean,
    styleOptions: Partial<CSSStyleDeclaration>
  ): void {
    if (!el) return;
    for (const [key, value] of Object.entries(styleOptions)) {
      if (key in el.style) {
        (el.style as any)[key] = isProcessing ? (value ?? '') : '';
      }
    }
    el.classList.toggle('disabled', isProcessing);
  }

  private toggleInteractiveElements(container: Element, isProcessing: boolean): void {
    const interactiveElements = container.querySelectorAll<HTMLElement>('button, a, input, select');
    interactiveElements.forEach(el => {
      el.toggleAttribute('disabled', isProcessing);
      el.style.pointerEvents = isProcessing ? 'none' : 'auto';
      el.style.backgroundColor = isProcessing ? 'transparent' : '';
    });
  }

  private buildCompleteData({
    technology = this.selectedTech?.value || 'angular',
    designLibrary = this.selectedDesign?.value || 'tailwindcss',
    includeImage,
    includeIds,
  }: {
    technology?: string;
    designLibrary?: string;
    includeImage: boolean;
    includeIds: boolean;
  }): CompleteSelections {
    const data: CompleteSelections = {
      type: this.selectedCardTitle,
      prompt: this.currentPrompt,
      application: 'web',
      technology,
      designLibrary,
      ...(includeImage && {
        imageUrl: this.submissionData.imageDataUri,
        imageDataUri: this.submissionData.imageDataUri,
      }),
      ...(includeIds && {
        projectId: this.projectId,
        jobId: this.jobId,
      }),
    };
    return data;
  }

  private handleProjectCreationError(message: string): void {
    this.isGenerating = false;
    if (this.isPolling) {
      this.pollingService.stopPolling();
      this.isPolling = false;
    }
    this.enablePromptBar();
    this.toastService.error(message);
  }

  private generateCodeWithProject(): void {
    const { imageDataUri } = this.submissionData;
    const hasImage = this.selectedFile && imageDataUri;
    const formFactor = 'web';
    const tech = this.selectedTech?.value || 'angular';
    const design = this.selectedDesign?.value || 'material';
    const userSignature = this.userSignatureService.getUserSignatureSync();
    if (hasImage) {
      const request = this.codeGenerationService.mapSelectionsToRequest(
        this.currentPrompt,
        formFactor,
        tech,
        design,
        imageDataUri!,
        userSignature, // Use the local userSignature variable
        this.projectId || undefined,
        this.selectedCardTitle
      );
      this.codeGenerationService.generateCode(request, userSignature).subscribe({
        next: response => this.handleCodeGenerationResponse(response, tech, design, true),
        error: () => this.handleProjectCreationError('Error generating code. Please try again.'),
      });
    } else {
      // Use the ServiceFactoryService to create the wireframe service and avoid circular dependencies
      this.serviceFactory.mapSelectionsToWireframeRequest(
        this.currentPrompt,
        formFactor,
        tech,
        design,
        this.projectId || undefined,
        this.selectedCardTitle
      )
      .then(wireframeRequest => {
        this.serviceFactory.generateWireframeCode(wireframeRequest, userSignature)
          .then(observable => {
            observable.subscribe({
              next: response => this.handleCodeGenerationResponse(response, tech, design, false),
              error: () => this.handleProjectCreationError('Error generating wireframe code. Please try again.')
            });
          })
          .catch(() => {
            this.handleProjectCreationError('Error generating wireframe code. Please try again.');
          });
      })
      .catch(() => {
        this.handleProjectCreationError('Error creating wireframe request. Please try again.');
      });
    }
  }

  private enablePromptBar(): void {
    this.isEnhancing = false;
    this.isGenerating = false;
    this.disablePromptBarElements(false);

    // Ensure the processing class is removed from the prompt bar
    const promptBar = document.querySelector('.prompt-bar') as HTMLElement;
    if (promptBar) {
      promptBar.classList.remove('processing');
    }

    setTimeout(() => {
      this.updateIconDisabledState();
      this.updateSendButtonState();
      if (this.enhanceClickCount < this.maxEnhanceClicks) {
        const enhanceButtonIcon = this.rightIcons[0];
        if (enhanceButtonIcon) {
          enhanceButtonIcon.status = 'active';
        }
      }
      this.disablePromptBarElements(false);
      this.adjustTextareaHeight();
      document.body.classList.remove('prompt-bar-processing');
    }, 0);
  }

  private handleCodeGenerationResponse(
    response: any,
    technology: string,
    designLibrary: string,
    hasImage: boolean
  ): void {
    let parsedResponse: any;
    try {
      parsedResponse = typeof response === 'string' ? JSON.parse(response) : response;
    } catch (e) {
      this.handleProjectCreationError('Unexpected response format.');
      return;
    }
    const { job_id, project_id } = parsedResponse || {};
    if (job_id && project_id) {
      this.jobId = job_id;
      this.projectId = project_id;
      const completeData = this.buildCompleteData({
        technology,
        designLibrary,
        includeImage: hasImage,
        includeIds: true,
      });
      this.appStateService.setCompleteSelections(completeData);
      this.promptService.setCompleteSelections(completeData);
      this.startPollingStatus();
    } else {
      const message = hasImage
        ? 'Error starting code generation. Please try again.'
        : 'Error starting wireframe code generation. Please try again.';
      this.handleProjectCreationError(message);
    }
  }

  private updateIconDisabledState(): void {
    requestAnimationFrame(() => {
      const isEmptyPrompt = !this.currentPrompt?.trim();
      const isProcessing = this.isProcessing();
      this.disablePromptBarElements(isProcessing);
      const enhanceIcons = document.querySelectorAll<HTMLElement>('.enhance-icons awe-icons');
      enhanceIcons.forEach((icon, index) => {
        const isEnhanceButton = index === 0;
        const isSendButton = index === 1;
        let isDisabled = isEmptyPrompt || isProcessing;
        if (isEnhanceButton) {
          isDisabled =
            isEmptyPrompt || isProcessing || this.enhanceClickCount >= this.maxEnhanceClicks;
          if (
            this.isPromptEnhanced &&
            !this.isEnhancedPromptValid &&
            this.enhanceClickCount < this.maxEnhanceClicks
          ) {
            isDisabled = isEmptyPrompt || isProcessing;
          }
        } else if (isSendButton) {
          if (this.isPromptEnhanced) {
            isDisabled = isEmptyPrompt || isProcessing || !this.isEnhancedPromptValid;
          } else {
            isDisabled = isEmptyPrompt || isProcessing;
          }
        }
        icon.classList.toggle('disabled', isDisabled);
        icon.style.cssText = `
          color: var(${isDisabled ? '--icon-disabled-color' : '--icon-enabled-color'});
          cursor: ${isDisabled ? 'not-allowed' : 'pointer'};
          opacity: ${isDisabled ? 'var(--icon-disabled-opacity, 0.9)' : '1'};
        `;
      });
    });
  }

  private updateTextareaValue(value: string): void {
    setTimeout(() => {
      const textarea = document.querySelector('.awe-prompt-bar textarea') as HTMLTextAreaElement;
      if (textarea) {
        textarea.value = value;
        const event = new Event('input', { bubbles: true });
        textarea.dispatchEvent(event);
        this.currentPrompt = value;
        this.cdr.detectChanges();
      }
    }, 0);
  }

  private adjustTextareaHeight(): void {
    const textAreas = document.querySelectorAll<HTMLTextAreaElement>('.prompt-text');
    if (!textAreas.length) return;
    let maxHeight = 0;
    textAreas.forEach(textArea => {
      textArea.style.height = 'auto';
      maxHeight = Math.max(maxHeight, textArea.scrollHeight);
    });
    const shouldApplyHeight = !!this.currentPrompt?.trim();
    const heightValue = shouldApplyHeight ? `${maxHeight}px` : '';
    textAreas.forEach(textArea => {
      textArea.style.height = heightValue;
    });
    this.cdr.detectChanges();
  }

  private startPollingStatus(): void {
    if (!this.projectId || !this.jobId) return;
    this.pollingService.startPolling(this.projectId, this.jobId, {
      taskType: 'image-to-code generation',
      initialInterval: 3000,
      maxInterval: 20000,
      backoffFactor: 1.5,
    });
    this.isPolling = true;
    this.isGenerating = true;
  }

  private handleEnhancedAlternate(): void {
    if (this.isEnhancing || this.isGenerating) {
      return;
    }
    if (this.isMaxFilesReached()) {
      this.fileError = `Only ${this.maxAllowedFiles} image can be uploaded at a time`;
      return;
    }
    const fileInput = this.createFileInput();
    fileInput.addEventListener('change', (event: Event) => this.handleFileSelect(event));
    fileInput.click();
  }

  private createFileInput(): HTMLInputElement {
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = 'image/*';
    fileInput.style.display = 'none';
    document.body.appendChild(fileInput);
    return fileInput;
  }

  private handleFileSelect(event: Event): void {
    const input = event.target as HTMLInputElement;
    const file = input.files?.[0];
    if (!file || !this.validateFile(file)) return;
    this.promptService.resetEnhancedPromptState();
    this.isPromptEnhanced = false;
    this.isEnhancedPromptValid = true;
    this.enhanceClickCount = 0;
    this.selectedFile = file;
    this.selectedFileName = file.name;
    this.promptService.setImage(file);
    const fileUrl = URL.createObjectURL(file);
    this.selectedFiles = [
      {
        id: Math.random().toString(36).substring(2, 11),
        name: file.name,
        url: fileUrl,
        type: file.type,
      },
    ];
    this.updateFileAttachPillStatus();
    const reader = new FileReader();
    reader.onload = e => this.updateSubmissionData(e, file, fileUrl);
    this.promptService.setPrompt('');
    this.currentPrompt = '';
    this.updateSendButtonState();

    // Update the animated text to image-specific prompts only if the prompt is empty
    if (!this.currentPrompt || this.currentPrompt.trim() === '') {
      this.animatedTexts = promptContentConstants.imageUploadAnimatedTexts;
      // Restart the typewriter effect with the new texts
      this.initTypewriterEffect();
    }

    reader.readAsDataURL(file);
    document.body.removeChild(event.target as HTMLElement);
  }

  private updateSubmissionData(e: ProgressEvent<FileReader>, file: File, fileUrl: string): void {
    this.submissionData = {
      ...this.submissionData,
      prompt: '', // Reset the prompt
      imageFile: file,
      imageUrl: fileUrl,
      imageDataUri: e.target?.result as string,
      fileName: file.name,
    };
  }

  private cleanupDomListeners(): void {
    if (this.boundHandlePasteEvent) {
      document.removeEventListener('paste', this.boundHandlePasteEvent);
    }
    if (this.textareaObserver) {
      this.textareaObserver.disconnect();
      this.textareaObserver = null;
    }
  }
}
