import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class TokenStorageService {
  private ACCESS_TOKEN_KEY = 'access_token';
  private REFRESH_TOKEN_KEY = 'refresh_token';
  private NAME_KEY = 'name';
  private USERNAME_KEY = 'username';
  private ID_TOKEN = 'id_token';

  private getCurrentDomain(): string {
    return window.location.hostname;
  }

  public setCookie(cname: string, cvalue: string, exseconds?: number): void {
    const d = new Date();
    let expires = '';
    if (exseconds) {
      d.setTime(d.getTime() + exseconds * 1000);
      expires = 'expires=' + d.toUTCString();
    }
    let secureFlag = window.location.protocol === 'https:' ? ';Secure' : '';
    let sameSiteFlag = ';SameSite=Lax';
    let domainFlag = `;domain=${this.getCurrentDomain()}`;
    document.cookie = `${cname}=${cvalue};${expires};path=/;${secureFlag}${sameSiteFlag}${domainFlag}`;
  }

  private getCookie(name: string): string | null {
    const match = document.cookie.match(new RegExp(`(^| )${name}=([^;]+)`));
    return match ? decodeURIComponent(match[2]) : null;
  }

  public deleteCookie(name: string): void {
    let domainFlag = `;domain=${this.getCurrentDomain()}`;
    document.cookie = `${name}=; path=/; max-age=0; Secure; SameSite=Strict${domainFlag}`;
  }

  storeTokens(accessToken: string, refreshToken: string, expiresInSeconds: number): void {
    this.setCookie(this.ACCESS_TOKEN_KEY, accessToken, expiresInSeconds);
    this.setCookie(this.REFRESH_TOKEN_KEY, refreshToken);
  }

  storeAccessToken(accessToken: string, expiresInSeconds?: number): void {
    this.setCookie(this.ACCESS_TOKEN_KEY, accessToken, expiresInSeconds);
  }

  storeInfo(name: string, username: string, idToken: string): void {
    this.setCookie(this.NAME_KEY, name);
    this.setCookie(this.USERNAME_KEY, username);
    this.setCookie(this.ID_TOKEN, idToken);
  }

  getAccessToken(): string | null {
    return this.getCookie(this.ACCESS_TOKEN_KEY);
  }

  getRefreshToken(): string | null {
    return this.getCookie(this.REFRESH_TOKEN_KEY);
  }

  getName(): string | null {
    return this.getCookie(this.NAME_KEY);
  }

  getUsername(): string | null {
    return this.getCookie(this.USERNAME_KEY);
  }

  getIdToken(): string | null {
    return this.getCookie(this.ID_TOKEN);
  }

  clearTokens(): void {
    this.deleteCookie(this.ACCESS_TOKEN_KEY);
    this.deleteCookie(this.REFRESH_TOKEN_KEY);
    this.deleteCookie(this.NAME_KEY);
    this.deleteCookie(this.USERNAME_KEY);
    this.deleteCookie(this.ID_TOKEN);
  }
}
