<awe-splitscreen
  class="container smooth-split-screen"
  [isResizable]="true"
  [minWidth]="(minWidth | async) || '300'"
  defaultLeftPanelWidth="35%"
  defaultRightPanelWidth="65%">
  <awe-leftpanel [hasHeader]="true" awe-leftpanel>
    <div awe-leftpanel-header>
      <!-- Custom left panel header instead of awe-header -->
      <div class="custom-header" [ngClass]="(currentTheme | async) + '-theme'">
        <div class="header-left">
          <awe-icons
            iconName="awe_home"
            iconColor="neutralIcon"
            class="cursor-pointer"
            (click)="navigateToHome()"
            title="Navigate to home page"></awe-icons>
          <awe-icons
            (click)="toggleLeftPanel()"
            iconName="awe_dock_to_right"
            iconColor="neutralIcon"></awe-icons>
        </div>
        <div class="header-center">
          <div class="project-name" [class.shimmer]="isProjectNameLoading" [class.hidden]="shouldHideProjectName | async" *ngIf="!(shouldHideProjectName | async)">
            {{ projectName }}
          </div>
        </div>
        <div class="header-right">
          <div class="custom-button" [class.active]="isHistoryActive | async">History</div>
        </div>
      </div>
    </div>
    <div awe-leftpanel-content class="adjust-height">
      <div *ngIf="!(isHistoryActive | async)" class="adjust-height">
        <!--Injected chat-window on left content-->
        <app-chat-window
          #chatWindow
          [theme]="(currentTheme | async) || 'light'"
          [defaultText]="'Ask me'"
          [rightIcons]="rightIcons"
          [(textValue)]="lightPrompt"
          [chatMessages]="lightMessages"
          [showStepper]="isPolling"
          [progress]="currentProgressState"
          [progressDescription]="lastProgressDescription"
          [status]="pollingStatus"
          [selectedImageDataUri]="selectedImageDataUri"
          [isCodeGenerationComplete]="isCodeGenerationComplete"
          (iconClicked)="handleIconClick($event)"
          (enterPressed)="handleEnhancedSendLight()"
          (retryStep)="onRetryClick()">
        </app-chat-window>
      </div>
      <div class="border history-container" *ngIf="isHistoryActive | async">
        <div class="history-content">
          <div class="history-header">
            <awe-icons iconName="awe_arrow_back_left" (click)="toggleHistoryView()"></awe-icons>
            <h4>Project History</h4>
          </div>

          <div class="history-cards-container"></div>
        </div>
      </div>
    </div>
  </awe-leftpanel>

  <awe-rightpanel [hasHeader]="true" awe-rightpanel>
    <div awe-rightpanel-header>
      <!-- Custom right panel header with tabs and organized icons -->
      <div class="custom-header" [ngClass]="(currentTheme | async) + '-theme'">
        <div class="header-left">
          <!-- Home icon that only shows when left panel is collapsed -->
          <awe-icons
            *ngIf="isLeftPanelCollapsed | async"
            iconName="awe_home"
            iconColor="neutralIcon"
            class="cursor-pointer"
            (click)="navigateToHome()"
            title="Navigate to home page"></awe-icons>
          <awe-icons
            class="dockToRight"
            *ngIf="isLeftPanelCollapsed | async"
            (click)="toggleLeftPanel()"
            iconName="awe_dock_to_right"
            iconColor="neutralIcon"
            title="Toggle left panel"></awe-icons>

          <!-- Tabs instead of toggle switch -->
          <div class="tabs-container">
            <!-- Preview tab - STRICT: only shown when DEPLOY + COMPLETED with ref_code URL -->
            <div
              *ngIf="isNewPreviewTabEnabled"
              class="custom-button"
              [class.active]="(currentView | async) === 'preview' && !(isLogsActive | async)"
              (click)="onTabClick('preview')"
              [title]="'View deployed application preview'">
              <span>Preview</span>
            </div>
            <!-- Code tab - STRICT: only shown when BUILD + IN_PROGRESS -->
            <div
              *ngIf="isNewCodeTabEnabled"
              class="custom-button"
              [class.active]="(currentView | async) === 'editor' && !(isLogsActive | async)"
              (click)="onTabClick('code')"
              [title]="'View generated code'">
              <span>Code</span>
            </div>
            <div
              class="custom-button"
              [class.active]="isLogsActive | async"
              [class.disabled]="!hasLogs"
              (click)="hasLogs && onTabClick('logs')"
              [title]="
                hasLogs
                  ? 'View application logs and debugging information'
                  : 'Logs will be available once generated'
              ">
              <span>Logs</span>
              <i *ngIf="isStreamingLogs" class="bi bi-arrow-repeat tab-status spinning"></i>
            </div>

            <!-- Artifacts tab - only shown and enabled when Project Overview state is received -->
            <div
              *ngIf="isArtifactsTabEnabled"
              class="custom-button"
              [class.active]="isArtifactsActive | async"
              (click)="onTabClick('artifacts')"
              [title]="'View project artifacts'">
              <span>Artifacts</span>
            </div>
            <!-- Export tab - shown like other tabs -->

          </div>
        </div>

        <div class="header-right">
          <div class="icon-group">
            <div
            *ngIf="isCodeGenerationComplete && !(previewError | async) && (currentView | async) === 'editor'"
              class="custom-button"
              [class.active]="isExperienceStudioModalOpen | async"
              (click)="toggleExportModal()"
              title="Export project">
              <span>Export</span>
            </div>
            <!-- Export icon removed as it's now in the tabs -->
            <awe-icons
              *ngIf="(currentView | async) === 'preview' && isCodeGenerationComplete && !(previewError | async)"
              iconName="awe_edit"
              iconColor="neutralIcon"
              title="Select element to edit"
              [class.active]="isElementSelectionMode"
              (click)="onEditButtonClick()"
              [disabled]="true"></awe-icons>
            <awe-icons
              *ngIf="
                (currentView | async) === 'preview' &&
                isCodeGenerationComplete &&
                !(previewError | async) &&
                (deployedUrl | async)
              "
              iconName="awe_external_link"
              iconColor="neutralIcon"
              title="Open preview in new tab"
              (click)="openPreviewInNewTab()"></awe-icons>
          </div>
        </div>
      </div>
    </div>

    <div awe-rightpanel-content>
      <!--Injected Code editor on right content-->
      <!-- <app-loading-animation
        *ngIf="currentView === 'loading' || isPreviewLoading"
        [messages]="loadingMessages"></app-loading-animation> -->
      <!-- Editor View - only show when editor is active AND logs are not active AND artifacts are not active -->
      <div *ngIf="(currentView | async) === 'editor' && !(isLogsActive | async) && !(isArtifactsActive | async)" class="editor-view">
        <!-- Always show loading animation when code generation is not complete -->
        <app-loading-animation
          *ngIf="!isCodeGenerationComplete"
          [messages]="loadingMessages"
          [theme]="(currentTheme | async) || 'light'"></app-loading-animation>

        <!-- Show code viewer when code generation is complete -->
        <app-code-viewer
          *ngIf="isCodeGenerationComplete"
          [theme]="(currentTheme | async) || 'light'"
          [files]="(files | async) || []"
          [showFileExplorer]="true"></app-code-viewer>
      </div>

      <!-- Preview View - only show when preview is active AND logs are not active AND artifacts are not active -->
      <div *ngIf="(currentView | async) === 'preview' && !(isLogsActive | async) && !(isArtifactsActive | async)" class="preview-view">
        <!-- Different states of the preview view - only one will be shown at a time -->

        <!-- Enhanced iframe loading with URL validation and availability checking -->
        <!-- Show URL validation loading state -->
        <div
          *ngIf="isCodeGenerationComplete && !(previewError | async) && urlSafe && !(isIframeReady | async)"
          class="url-validation-container">
          <app-loading-animation
            [theme]="(currentTheme | async) || 'light'"
            [messages]="[
              'Validating deployment URL...',
              'Checking URL accessibility...',
              'Preparing iframe for loading...',
              'Almost ready to display your app...'
            ]">
          </app-loading-animation>

          <!-- Show validation error if any -->
          <div *ngIf="(urlValidationError | async)" class="url-validation-error">
            <div class="error-message">
              <i class="bi bi-exclamation-triangle"></i>
              <span>{{ urlValidationError | async }}</span>
            </div>
          </div>
        </div>

        <!-- Show iframe only when URL is validated, available, and iframe is ready -->
        <div
          *ngIf="isCodeGenerationComplete && !(previewError | async) && urlSafe && (isIframeReady | async) && (isUrlValidated | async) && (isUrlAvailable | async)"
          class="iframe-container">
          <iframe
            [src]="urlSafe"
            class="preview-frame"
            frameborder="0"
            (load)="onIframeLoad($event)"
            (error)="onIframeError($event)"
            sandbox="allow-same-origin allow-scripts allow-popups allow-forms allow-modals"
            referrerpolicy="no-referrer"
            allow="accelerometer; camera; encrypted-media; geolocation; gyroscope; microphone; midi">
          </iframe>
        </div>

        <!-- Show deployment loading state when code is complete but no URL yet -->
        <div
          *ngIf="isCodeGenerationComplete && !(previewError | async) && !urlSafe"
          class="deployment-loading-container">
          <app-loading-animation
            [theme]="(currentTheme | async) || 'light'"
            [messages]="[
              'Preparing your preview deployment...',
              'Setting up hosting environment...',
              'Configuring deployment pipeline...',
              'Building application for preview...',
              'Deploying to preview server...',
              'Almost ready - finalizing deployment...',
              'Your preview will be available shortly...'
            ]">
          </app-loading-animation>
        </div>

        <!-- Initial loading state - show if code generation is not complete and either:
             1. No progress state exists, or
             2. We're in a state that's not LAYOUT_ANALYZED or PAGES_GENERATED, or
             3. We're in LAYOUT_ANALYZED or PAGES_GENERATED but have no valid layout data -->
        <ng-container
          *ngIf="
            !isCodeGenerationComplete &&
            (!currentProgressState ||
              (!currentProgressState.includes('LAYOUT_ANALYZED') &&
                !currentProgressState.includes('PAGES_GENERATED')) ||
              ((currentProgressState.includes('LAYOUT_ANALYZED') ||
                currentProgressState.includes('PAGES_GENERATED')) &&
                (!layoutData || layoutData.length === 0 || !layoutMapping[layoutData[0]])))
          ">
          <div
            class="loading-state-container"
            [style.display]="isCodeGenerationComplete || (previewError | async) ? 'none' : 'block'">
            <app-loading-animation
              [messages]="loadingMessages"
              [theme]="(currentTheme | async) || 'light'"></app-loading-animation>
          </div>
        </ng-container>

        <!-- Layout analyzed state - only show if code generation is not complete -->
        <ng-container
          *ngIf="
            !isCodeGenerationComplete &&
            currentProgressState &&
            currentProgressState.includes('LAYOUT_ANALYZED') &&
            !currentProgressState.includes('PAGES_GENERATED') &&
            layoutData &&
            layoutData.length > 0 &&
            layoutMapping[layoutData[0]]
          ">
          <div
            class="layout-examples-container"
            [style.display]="isCodeGenerationComplete || (previewError | async) ? 'none' : 'block'">
            <div class="layout-examples-header">
              <h3>{{ layoutData.length > 1 ? 'Layouts Identified' : 'Layout Identified' }}</h3>
              <p *ngIf="layoutData.length > 1">
                {{ layoutData.length }} layouts have been identified for your application
              </p>
            </div>

            <!-- Show actual layout data - no shimmer/skeleton loading -->
            <div
              class="layout-examples-grid"
              [class.layout-examples-grid-fullscreen]="layoutData.length === 1"
              [class.layout-count-1]="layoutData.length === 1"
              [class.layout-count-2]="layoutData.length === 2"
              [class.layout-count-3]="layoutData.length === 3"
              [class.layout-count-4]="layoutData.length === 4">
              <!-- If we have layout data, use it and show all identified layouts -->
              <ng-container *ngIf="layoutData && layoutData.length > 0">
                <div
                  *ngFor="let layout of layoutData; trackBy: trackByLayoutId"
                  class="layout-example-item"
                  [class.layout-example-item-fullscreen]="layoutData.length === 1">
                  <div class="layout-example-card">
                    <div
                      class="layout-example-image"
                      [class.layout-example-image-fullscreen]="layoutData.length === 1">
                      <!-- Directly use the layout key to get the image with layout- prefix -->
                      <img
                        [src]="'assets/images/layout-' + layout + '.png'"
                        [alt]="layoutMapping[layout] || 'Identified Layout'"
                        loading="lazy"
                        decoding="async"
                        onerror="this.src='assets/images/01.png'" />
                    </div>
                    <div class="layout-example-title">
                      {{ layoutMapping[layout] || 'Identified Layout' }}
                    </div>
                  </div>
                </div>
              </ng-container>

              <!-- Fallback to default layout if none detected -->
              <ng-container *ngIf="!layoutData || layoutData.length === 0">
                <div class="layout-example-item layout-example-item-fullscreen">
                  <div class="layout-example-card">
                    <div class="layout-example-image layout-example-image-fullscreen">
                      <img
                        [src]="'assets/images/01.png'"
                        [alt]="'Default Layout'"
                        loading="lazy"
                        decoding="async" />
                    </div>
                    <div class="layout-example-title">Default Layout Structure</div>
                    <div class="layout-example-description">
                      A standard layout structure has been selected for your application
                    </div>
                  </div>
                </div>
              </ng-container>
            </div>
          </div>
        </ng-container>

        <!-- Pages generated state - only show if code generation is not complete -->
        <ng-container
          *ngIf="
            !isCodeGenerationComplete &&
            currentProgressState &&
            currentProgressState.includes('PAGES_GENERATED') &&
            layoutData &&
            layoutData.length > 0 &&
            layoutMapping[layoutData[0]]
          ">
          <div
            class="pages-generated-container"
            [style.display]="isCodeGenerationComplete || (previewError | async) ? 'none' : 'block'">
            <div class="pages-generated-header">
              <h3>Page Inventory</h3>
              <p *ngIf="layoutData.length > 1">
                Using {{ layoutData.length }} different layout types for your pages
              </p>
            </div>

            <!-- Show actual page data when loaded -->
            <div
              class="pages-examples-grid"
              [class.layout-count-1]="layoutData.length === 1"
              [class.layout-count-2]="layoutData.length === 2"
              [class.layout-count-3]="layoutData.length === 3"
              [class.layout-count-4]="layoutData.length === 4">
              <!-- If we have layout data, use it - only show the specific key -->
              <ng-container *ngIf="layoutData && layoutData.length > 0">
                <!-- Create page examples using all available layouts -->
                <div *ngFor="let i of [0, 1, 2, 3, 4, 5, 6, 7]; trackBy: trackByPageIndex" class="page-example-item">
                  <div class="page-example-card">
                    <div class="page-example-image">
                      <!-- Use the appropriate layout based on index -->
                      <img
                        [src]="'assets/images/layout-' + getLayoutForPageIndex(i) + '.png'"
                        [alt]="getPageTitle(i, getLayoutForPageIndex(i))"
                        loading="lazy"
                        decoding="async"
                        onerror="this.src='assets/images/0' + (i + 1 > 8 ? '1' : (i + 1)) + '.png'"
                        [attr.data-index]="i + 1" />
                    </div>
                    <div class="page-example-title">
                      {{ getPageTitle(i, getLayoutForPageIndex(i)) }}
                    </div>
                  </div>
                </div>
              </ng-container>

              <!-- Fallback to default pages if none detected -->
              <ng-container *ngIf="!layoutData || layoutData.length === 0">
                <div
                  *ngFor="let image of layoutExampleImages.slice(0, 8); let i = index; trackBy: trackByPageIndex"
                  class="page-example-item">
                  <div class="page-example-card">
                    <div class="page-example-image">
                      <img
                        [src]="image"
                        [alt]="getPageTitle(i, '')"
                        loading="lazy"
                        decoding="async" />
                    </div>
                    <div class="page-example-title">
                      {{ getPageTitle(i, '') }}
                    </div>
                    <!-- <div class="page-example-description">
                      Standard page structure for your website
                    </div> -->
                  </div>
                </div>
              </ng-container>
            </div>
          </div>
        </ng-container>

        <!-- Show error page if preview failed -->
        <app-error-page
          *ngIf="previewError | async"
          [theme]="(currentTheme | async) || 'light'"
          [errorDescription]="(errorDescription | async) || 'An error occurred'"
          [terminalOutput]="(errorTerminalOutput | async) || ''"
          [progressState]="pollingStatus"
          (retry)="onRetryClick()"
          (goHome)="navigateToHome()"
          (showDetails)="toggleLogsView()">
        </app-error-page>
      </div>

      <!-- Logs View - only show when logs are active -->
      <div *ngIf="(currentView | async) === 'logs' && (isLogsActive | async) && !(isArtifactsActive | async)" class="logs-view">
        <!-- Show loading animation when no logs are available -->
        <app-loading-animation
          *ngIf="!hasLogs"
          [theme]="(currentTheme | async) || 'light'"
          [messages]="[
            'Initializing your workspace…',
            'Loading creative modules…',
            'Setting up your dashboard…',
            'Almost there — preparing magic!',
            'Initializing build process and preparing environment...',
            'Setting up compilation pipeline for your application...',
            'Configuring build tools and dependencies...',
            'Preparing to generate application logs...',
            'Logs will appear here as they become available...',
            'Monitoring build process for status updates...',
          ]">
        </app-loading-animation>

        <!-- Show logs when they are available -->
        <div *ngIf="hasLogs || isCodeGenerationComplete" class="logs-container">
          <div class="logs-header">
            <div class="logs-header-row">
              <div class="logs-header-left">
                <div class="logs-header-title">
                  <h4>Application Logs</h4>
                </div>
              </div>

              <div class="logs-header-center">
                <div class="log-filter">
                  <div
                    class="filter-option"
                    [class.active]="true"
                    [class.disabled]="!isCodeGenerationComplete">
                    <span class="dot info-dot"></span>
                    <span>Info</span>
                  </div>
                  <div
                    class="filter-option"
                    [class.active]="true"
                    [class.disabled]="!isCodeGenerationComplete">
                    <span class="dot debug-dot"></span>
                    <span>Debug</span>
                  </div>
                  <div
                    class="filter-option"
                    [class.active]="true"
                    [class.disabled]="!isCodeGenerationComplete">
                    <span class="dot warning-dot"></span>
                    <span>Warning</span>
                  </div>
                  <div
                    class="filter-option"
                    [class.active]="true"
                    [class.disabled]="!isCodeGenerationComplete">
                    <span class="dot error-dot"></span>
                    <span>Error</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="logs-content">
            <!-- Info message removed as requested -->

            <!-- New formatted logs display with letter-by-letter typing effect -->
            <div class="formatted-logs-container">
              <!-- Regular log entries with trackBy for more efficient rendering -->
              <div
                *ngFor="let log of formattedLogMessages; let i = index; trackBy: trackByLogIndex"
                class="log-entry"
                [class]="getLogClass(log)"
                [class.new-log]="i >= formattedLogMessages.length - 5">
                <!-- Timestamp -->
                <span class="log-timestamp">{{ log.timestamp }}</span>

                <!-- Code block with capsule-like container -->
                <div
                  *ngIf="log.type === 'code'"
                  class="code-capsule"
                  [class.expanded]="isCodeExpanded(log.id)"
                  [class.dark-theme]="(currentTheme | async) === 'dark'">
                  <!-- Code header with file path and toggle button -->
                  <div class="code-header" (click)="toggleCodeExpansion(log)">
                    <span class="file-path">{{ log.path || 'unknown.file' }}</span>
                    <div class="code-header-actions">
                      <span class="toggle-icon">
                        {{ isCodeExpanded(log.id) ? '▼' : '▶' }}
                      </span>
                    </div>
                  </div>

                  <!-- Code content with typing effect - only shown when expanded -->
                  <div
                    class="code-content-wrapper"
                    [class.expanded]="isCodeExpanded(log.id)"
                    [style.--max-content-height.px]="log.maxHeight || 500">
                    <pre class="code-content">
                      <code
                        [innerHTML]="formatCodeForDisplay(log.visibleContent || '')"
                        [class.typing]="isTypingLog && i === currentLogIndex"></code>
                      <!-- Typewriter cursor removed as requested -->
                    </pre>
                  </div>
                </div>

                <!-- Regular log content with typing effect - only show if content is not empty -->
                <span
                  *ngIf="log.type !== 'code'"
                  class="log-content"
                  [class.typing]="isTypingLog && i === currentLogIndex">
                  <span class="log-text">{{ log.visibleContent || '' }}</span>
                  <!-- Typewriter cursor removed as requested -->
                </span>
              </div>

              <!-- Show a message when no logs are available -->
              <div *ngIf="formattedLogMessages.length === 0" class="no-logs-message">
                <span
                  >No logs available yet. Logs will appear here when the application generates
                  them.</span
                >
              </div>
            </div>

            <!-- Keep the original logs display for compatibility, but hide it -->
            <pre style="display: none">
              <code *ngFor="let log of logMessages; let i = index" [class]="getLogClass(log)">{{ log }}</code>
            </pre>
          </div>
        </div>
      </div>

      <!-- Artifacts View - only show when artifacts tab is active and enabled -->
      <div *ngIf="(currentView | async) === 'artifacts' && (isArtifactsActive | async) && isArtifactsTabEnabled" class="artifacts-view">
        <!-- Show loading animation when artifacts are not available -->
        <app-loading-animation
          *ngIf="!isArtifactsTabEnabled || artifactsData.length === 0"
          [theme]="(currentTheme | async) || 'light'"
          [messages]="[
            'Loading project artifacts...',
            'Preparing file viewer...',
            'Organizing project resources...',
            'Collecting design assets...',
            'Analyzing project structure...',
            'Gathering documentation...',
          ]">
        </app-loading-animation>

        <!-- Show artifacts when they are available -->
        <div *ngIf="isArtifactsTabEnabled && artifactsData.length > 0" class="artifacts-container">
          <!-- File viewer with split view -->
          <div class="artifacts-content">
            <!-- Left sidebar with file tree -->
            <div class="file-explorer">
               <div class="file-explorer-header">
                <span>Files</span>
              </div>
              <div class="file-list">
                <div
                  *ngFor="let file of artifactsData"
                  class="file-item"
                  [class.selected]="selectedArtifactFile === file"
                  (click)="selectArtifactFile(file)">
                  <span class="file-icon" [ngClass]="getFileIconClass(file.type)"></span>
                  <span class="file-name">{{ file.name }}</span>
                </div>
              </div>
            </div>

            <!-- Right content area -->
            <div class="file-content">
              <!-- Show message when no file is selected -->
              <div *ngIf="!selectedArtifactFile" class="no-file-selected">
                <span>Select a file from the list to view its content</span>
              </div>

              <!-- Show content based on file type -->
              <ng-container *ngIf="selectedArtifactFile">
                <!-- Markdown content -->
                <div *ngIf="selectedArtifactFile.type === 'markdown'" class="markdown-content">
                  <markdown [data]="selectedArtifactFile.content"></markdown>
                </div>

                <!-- Image content -->
                <div *ngIf="selectedArtifactFile.type === 'image'" class="image-content">
                  <!-- Layout Analyzed content -->
                  <div *ngIf="selectedArtifactFile.name === 'Layout Analyzed'" class="layout-examples-container">
                    <div class="layout-examples-header">
                      <h3>{{ layoutAnalyzedData && layoutAnalyzedData.length > 1 ? 'Layouts Identified' : 'Layout Identified' }}</h3>
                      <p *ngIf="layoutAnalyzedData && layoutAnalyzedData.length > 1">
                        {{ layoutAnalyzedData.length }} layouts have been identified for your application
                      </p>
                      <p *ngIf="layoutAnalyzedData && layoutAnalyzedData.length === 1">
                        A layout has been identified for your application
                      </p>
                    </div>

                    <!-- Show actual layout data -->
                    <div
                      class="layout-examples-grid"
                      [class.layout-examples-grid-fullscreen]="layoutAnalyzedData.length === 1"
                      [class.layout-count-1]="layoutAnalyzedData.length === 1"
                      [class.layout-count-2]="layoutAnalyzedData.length === 2"
                      [class.layout-count-3]="layoutAnalyzedData.length === 3"
                      [class.layout-count-4]="layoutAnalyzedData.length === 4">
                      <!-- If we have layout data, use it and show all identified layouts -->
                      <ng-container *ngIf="layoutAnalyzedData && layoutAnalyzedData.length > 0">
                        <div
                          *ngFor="let layout of layoutAnalyzedData; trackBy: trackByLayoutId"
                          class="layout-example-item"
                          [class.layout-example-item-fullscreen]="layoutAnalyzedData.length === 1">
                          <div class="layout-example-card">
                            <div
                              class="layout-example-image"
                              [class.layout-example-image-fullscreen]="layoutAnalyzedData.length === 1">
                              <!-- Use the image URL from the layout data -->
                              <img
                                [src]="layout.imageUrl"
                                [alt]="layout.name"
                                loading="lazy"
                                decoding="async"
                                fetchpriority="auto"
                                (error)="layout.imageUrl = 'assets/images/01.png'"
                                (click)="showImagePreview(layout.imageUrl, layout.name)" />
                            </div>
                            <div class="layout-example-title">
                              {{ layout.name }}
                            </div>
                            <div class="layout-example-description" *ngIf="layoutAnalyzedData.length === 1">
                              This layout structure will be used for your application
                            </div>
                          </div>
                        </div>
                      </ng-container>

                      <!-- Fallback to default layout if none detected -->
                      <ng-container *ngIf="!layoutAnalyzedData || layoutAnalyzedData.length === 0">
                        <div class="layout-example-item layout-example-item-fullscreen">
                          <div class="layout-example-card">
                            <div class="layout-example-image layout-example-image-fullscreen">
                              <img
                                [src]="'assets/images/01.png'"
                                [alt]="'Default Layout'"
                                loading="lazy"
                                decoding="async" />
                            </div>
                            <div class="layout-example-title">Default Layout Structure</div>
                            <div class="layout-example-description">
                              A standard layout structure has been selected for your application
                            </div>
                          </div>
                        </div>
                      </ng-container>
                    </div>
                  </div>

                  <!-- Regular image content for other image types -->
                  <img
                    *ngIf="selectedArtifactFile.name !== 'Layout Analyzed'"
                    [src]="selectedArtifactFile.content"
                    [alt]="selectedArtifactFile.name"
                    loading="lazy"
                    decoding="async" />
                </div>

                <!-- SVG content -->
                <div *ngIf="selectedArtifactFile.type === 'svg'" class="svg-content">
                  <img
                    [src]="selectedArtifactFile.content"
                    [alt]="selectedArtifactFile.name"
                    loading="lazy"
                    decoding="async" />
                </div>

                <!-- Text content -->
                <div *ngIf="selectedArtifactFile.type === 'text'" class="text-content">
                  <pre>{{ selectedArtifactFile.content }}</pre>
                </div>

                <!-- Component content (for design system) -->
                <div *ngIf="selectedArtifactFile.type === 'component'" class="component-content">
                  <!-- Design System Component -->
                  <div *ngIf="selectedArtifactFile.name === 'Design System'" class="design-system-container">
                    <!-- Design System Header -->
                    <div class="design-system-header">
                      <h2>Design System</h2>
                      <p class="design-system-subtitle">Customize your application's visual identity</p>
                    </div>

                    <!-- Colors Section -->
                    <div class="design-section colors-section">
                      <div class="section-header">
                        <h3>Colors</h3>
                        <p class="section-description">Define your brand colors and theme palette</p>
                      </div>
                      <div class="color-swatches">
                        <div *ngFor="let token of getTokensByCategory('Colors')" class="color-swatch">
                          <div class="color-preview">
                            <div class="color-box" [style.background-color]="token.value"></div>
                            <div class="color-info">
                              <div class="color-name">{{ token.name }}</div>
                              <div class="color-value">
                                <input
                                  type="text"
                                  class="token-input"
                                  [value]="token.value"
                                  (change)="onTokenValueChange($event, token.id)"
                                  [attr.title]="'Edit ' + token.name + ' color value'" />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Divider -->
                    <div class="design-section-divider"></div>

                    <!-- Typography Section -->
                    <div class="design-section typography-section">
                      <div class="section-header">
                        <h3>Typography</h3>
                        <p class="section-description">Text styles and font hierarchy for your application</p>
                      </div>
                      <div class="typography-samples">
                        <div *ngFor="let token of getTokensByCategory('Font Style Desktop')" class="typography-item">
                          <div class="typography-preview">
                            <h2 *ngIf="token.name === 'Headline 1'" class="headline-1">{{ token.name }}</h2>
                            <h3 *ngIf="token.name === 'Headline 2'" class="headline-2">{{ token.name }}</h3>
                            <h4 *ngIf="token.name === 'Headline 3'" class="headline-3">{{ token.name }}</h4>
                            <h5 *ngIf="token.name === 'Headline 4'" class="headline-4">{{ token.name }}</h5>
                          </div>
                          <div class="typography-details">
                            <span class="typography-specs">{{ token.value }}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Divider -->
                    <div class="design-section-divider"></div>

                    <!-- Buttons Section -->
                    <div class="design-section buttons-section">
                      <div class="section-header">
                        <h3>Buttons</h3>
                        <p class="section-description">Interactive elements and call-to-action styles</p>
                      </div>
                      <div class="button-samples">
                        <div *ngFor="let token of getTokensByCategory('Buttons')" class="button-sample">
                          <div class="button-preview">
                            <button [class]="'sample-button ' + token.value">
                              {{ token.name }}
                              <span *ngIf="token.value.includes('icon')" class="button-icon">✨</span>
                            </button>
                          </div>
                          <div class="button-details">
                            <span class="button-type">{{ token.value | titlecase }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Placeholder for other component types -->
                  <div *ngIf="selectedArtifactFile.name !== 'Design System'" class="shimmer-container">
                    <div class="shimmer-block"></div>
                    <div class="shimmer-block"></div>
                    <div class="shimmer-block"></div>
                  </div>
                </div>
              </ng-container>
            </div>
          </div>
        </div>
      </div>
    </div>
  </awe-rightpanel>
</awe-splitscreen>

<!-- Custom Modal Implementation -->
<div
  class="custom-modal-overlay"
  *ngIf="isExperienceStudioModalOpen | async"
  (click)="toggleExportModal()">
  <div class="custom-modal-content" (click)="$event.stopPropagation()">
    <div class="custom-modal-header">
      <h3>Export this project</h3>
      <button class="custom-close-button" (click)="toggleExportModal()">×</button>
    </div>
    <div class="custom-modal-body">
      <div class="sharable-link-section">
        <p class="section-label">Sharable link</p>
        <div class="link-input-container">
          <input
            class="link-input"
            type="text"
            [value]="(deployedUrl | async) || 'Deployment URL will be available after deployment completes'"
            readonly
            #sharableLink />
          <button class="copy-button" (click)="copyToClipboard(sharableLink)">Copy</button>
        </div>
      </div>

      <div class="export-options">
        <div class="export-option-row">
          <div class="export-option-item" (click)="downloadProject()">
            <div class="option-icon-container download">
              <img
                src="assets/icons/awe_download.svg"
                [ngClass]="{ 'dark-icon': (currentTheme | async) === 'dark' }"
                alt="Download"
                width="32"
                height="32"
                loading="lazy"
                decoding="async" />
            </div>
            <span class="option-label">Download</span>
          </div>

          <div class="export-option-item" (click)="exportToAzure()">
            <div class="option-icon-container">
              <img
                src="assets/icons/awe_azure.svg"
                width="32"
                height="32"
                loading="lazy"
                decoding="async" />
            </div>
            <span class="option-label">Azure</span>
          </div>

          <div class="export-option-item" (click)="exportToVSCode()">
            <div class="option-icon-container">
              <img
                src="assets/icons/awe_vscode.svg"
                width="32"
                height="32"
                loading="lazy"
                decoding="async" />
            </div>
            <span class="option-label">VSCode</span>
          </div>
        </div>


      </div>
    </div>
  </div>
</div>

<!-- Simple Full Screen Image Preview Overlay -->
<div class="image-overlay" *ngIf="showPreview && previewImage">
  <button
    class="close-button"
    (click)="closeImagePreview()"
    role="button"
    tabindex="0"
    aria-label="Close preview">
    ×
  </button>
  <img
    [src]="previewImage.url"
    [alt]="previewImage.name"
    loading="eager"
    decoding="async"
    fetchpriority="high" />
</div>
