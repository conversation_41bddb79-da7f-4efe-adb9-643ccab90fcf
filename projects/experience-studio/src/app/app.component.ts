import { AfterContentChecked, ChangeDetectorRef, Component, OnInit, OnDestroy } from '@angular/core';
import { RouterOutlet, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { NavHeaderComponent } from './shared/components/nav-header/nav-header.component';
import { AuthService } from './authentication/auth-service/auth.service';
import { TokenStorageService } from './authentication/token-storage/token-storage.service';
import { AuthTokenService } from './authentication/token-service/auth-token.service';
import { ThemeService } from './shared/services/theme-service/theme.service';
import { ToastService } from './shared/services/toast.service';
import { Subject, Subscription } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { createLogger } from './shared/utils';
import { UserProfile } from './shared/models/user-profile.model';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet, NavHeaderComponent, CommonModule],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
})
export class AppComponent implements OnInit, AfterContentChecked, OnDestroy {
  title = 'experienceStudio';

  // Navigation and UI state
  public showExpandedNav: boolean = true;


  // Authentication state
  public isLoggedIn: boolean = false;
  public userProfile: UserProfile | undefined = undefined;

  // Token management
  public checkIntervalMs = 4000;
  public tokenCheckInterval: any;
  public subscription = new Subscription();

  // Theme management
  public isThemeLoaded: boolean = false;

  // Debug panel (show in development)
  public showDebugPanel: boolean = true; // Set to false for production

  // Lifecycle management
  private readonly _destroying$ = new Subject<void>();
  private logger = createLogger('AppComponent');

  constructor(
    private cdr: ChangeDetectorRef,
    private router: Router,
    public authService: AuthService,
    private tokenStorageService: TokenStorageService,
    private authTokenService: AuthTokenService,
    private themeService: ThemeService,
    private toastService: ToastService
  ) {
    // Make debugging methods available globally for console access
    (window as any).debugAuth = {
      checkAuth: () => this.checkAuthenticationStatus(),
      triggerLogin: () => this.triggerLogin(),
      clearTokens: () => this.clearTokensAndLogout(),
      getTokens: () => this.getTokenInfo(),
      logUserDetails: () => this.logUserDetails()
    };
  }

  public ngOnInit(): void {
    this.initializeApp();
  }

  private async initializeApp(): Promise<void> {
    try {
      this.logger.info('Initializing Experience Studio application...');

      // Handle authentication code and token from URL
      this.authTokenService.handleAuthCodeAndToken();

      // Start token checking
      this.authTokenService.startTokenCheck();

      // Subscribe to authentication state changes
      this.authService.authState$
        .pipe(takeUntil(this._destroying$))
        .subscribe((isAuthenticated: boolean) => {
          this.logger.info(`Authentication state changed: ${isAuthenticated ? 'AUTHENTICATED' : 'NOT AUTHENTICATED'}`);
          this.isLoggedIn = isAuthenticated;

          // Load theme when user is authenticated and theme not loaded yet
          if (isAuthenticated && !this.isThemeLoaded) {
            this.themeService.setTheme(this.themeService.getCurrentTheme());
            this.isThemeLoaded = true;
            this.logger.info('Theme loaded successfully');
          }

          // Get user info when authenticated
          if (isAuthenticated) {
            this.loadUserProfile();
            this.logUserDetails();
            this.toastService.success('Successfully authenticated!');
          } else {
            this.userProfile = undefined;
            this.logger.info('User logged out - profile cleared');
          }

          // Trigger change detection to update UI
          this.cdr.detectChanges();
        });

      // Check initial authentication state
      const isInitiallyAuthenticated = this.authService.isAuthenticated();
      this.logger.info(`Initial authentication check: ${isInitiallyAuthenticated ? 'AUTHENTICATED' : 'NOT AUTHENTICATED'}`);

      if (isInitiallyAuthenticated) {
        this.isLoggedIn = true;
        this.loadUserProfile();
        this.logUserDetails();

        if (!this.isThemeLoaded) {
          this.themeService.setTheme(this.themeService.getCurrentTheme());
          this.isThemeLoaded = true;
          this.logger.info('Theme loaded successfully');
        }
      } else {
        // If not authenticated, check if we need to redirect to login
        const currentPath = window.location.pathname;
        if (!currentPath.includes('/login') && !currentPath.includes('/callback')) {
          this.logger.info('User not authenticated, redirecting to login...');
          // Automatically redirect to login for better UX
          this.triggerLogin();
          return;
        }
      }

      this.logger.info('Application initialization completed');

    } catch (error) {
      this.logger.error('Error during app initialization:', error);
      this.toastService.error('Failed to initialize application');
    }
  }

  private loadUserProfile(): void {
    try {
      // Get user info from token storage
      const name = this.tokenStorageService.getName();
      const username = this.tokenStorageService.getUsername();

      if (name && username) {
        this.userProfile = {
          displayName: name,
          mail: username,
          userPrincipalName: username,
          photoUrl: undefined
        };
        this.logger.info('User profile loaded successfully');
      } else {
        this.logger.warn('User profile data not found in token storage');
      }
    } catch (error) {
      this.logger.error('Error loading user profile:', error);
    }
  }

  private logUserDetails(): void {
    try {
      const accessToken = this.tokenStorageService.getAccessToken();
      const refreshToken = this.tokenStorageService.getRefreshToken();
      const name = this.tokenStorageService.getName();
      const username = this.tokenStorageService.getUsername();
      const idToken = this.tokenStorageService.getIdToken();

      this.logger.info(' === USER AUTHENTICATION DETAILS ===');
      this.logger.info(`User Name: ${name || 'Not available'}`);
      this.logger.info(`User Email: ${username || 'Not available'}`);
      this.logger.info(`Access Token: ${accessToken ? 'Present' : 'Missing'}`);
      this.logger.info(`Refresh Token: ${refreshToken ? 'Present' : 'Missing'}`);
      this.logger.info(`ID Token: ${idToken ? 'Present' : 'Missing'}`);
      this.logger.info(`Authentication Status: ${this.isLoggedIn ? 'AUTHENTICATED' : 'NOT AUTHENTICATED'}`);

      if (this.userProfile) {
        this.logger.info(' User Profile Object:', this.userProfile);
      }

      this.logger.info(' === END USER DETAILS ===');
    } catch (error) {
      this.logger.error(' Error logging user details:', error);
    }
  }

  public checkAuthenticationStatus(): void {
    this.logger.info(' === MANUAL AUTHENTICATION CHECK ===');
    const isAuth = this.authService.isAuthenticated();
    this.logger.info(`Current authentication status: ${isAuth ? ' AUTHENTICATED' : 'NOT AUTHENTICATED'}`);

    if (isAuth) {
      this.logUserDetails();
    } else {
      this.logger.info('User is not authenticated. No user details available.');
    }
    this.logger.info(' === END MANUAL CHECK ===');
  }

  public triggerLogin(): void {
    try {
      this.logger.info(' Triggering manual login...');
      this.router.navigate(['/login']);
    } catch (error) {
      this.logger.error(' Error triggering login:', error);
      this.toastService.error('Failed to initiate login');
    }
  }

  public clearTokensAndLogout(): void {
    try {
      this.logger.info('Clearing all tokens and logging out...');
      this.tokenStorageService.clearTokens();
      // The AuthService will automatically update the auth state when tokens are cleared
      this.isLoggedIn = false;
      this.userProfile = undefined;
      this.logger.info(' Tokens cleared and user logged out');
      this.toastService.info('Logged out successfully');
      // Navigate to login page
      this.router.navigate(['/login']);
    } catch (error) {
      this.logger.error('Error clearing tokens:', error);
      this.toastService.error('Failed to clear tokens');
    }
  }

  public getTokenInfo(): any {
    try {
      const tokenInfo = {
        accessToken: this.tokenStorageService.getAccessToken(),
        refreshToken: this.tokenStorageService.getRefreshToken(),
        name: this.tokenStorageService.getName(),
        username: this.tokenStorageService.getUsername(),
        idToken: this.tokenStorageService.getIdToken(),
        isAuthenticated: this.authService.isAuthenticated()
      };

      this.logger.info(' Current token information:', tokenInfo);
      return tokenInfo;
    } catch (error) {
      this.logger.error('Error getting token info:', error);
      return null;
    }
  }

  ngAfterContentChecked(): void {
    this.cdr.detectChanges();
  }

  public onLogout(): void {
    try {
      const currentUrl = window.location.origin;
      this.authService.logout(currentUrl).subscribe({
        next: () => {
          this.logger.debug('Logout successful');
        },
        error: (error) => {
          this.logger.error('Logout failed:', error);
          // Clear tokens locally even if logout API fails
          this.tokenStorageService.clearTokens();
          this.isLoggedIn = false;
          this.userProfile = undefined;
          this.router.navigate(['/login']);
        }
      });
    } catch (error) {
      this.logger.error('Error during logout:', error);
      this.toastService.error('Logout failed');
    }
  }

  ngOnDestroy(): void {
    this._destroying$.next();
    this._destroying$.complete();
    this.authTokenService.stopTokenCheck();
    this.subscription.unsubscribe();
  }
}
