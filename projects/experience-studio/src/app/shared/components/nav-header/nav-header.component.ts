import { Component, OnInit, OnDestroy, Input, Output, EventEmitter, ChangeDetectionStrategy, ChangeDetectorRef, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonComponent, HeaderComponent, HeadingComponent } from '@awe/play-comp-library';

import { ThemeService } from '../../services/theme-service/theme.service';
import { UserProfile } from '../../models/user-profile.model';
import { AppConstants } from '../../appConstants';
import { ObserverManager } from '../../utils/subscription-management.util';
import { createLogger } from '../../utils/logger';

@Component({
  selector: 'app-nav-header',
  imports: [HeaderComponent, CommonModule, ButtonComponent, HeadingComponent],
  standalone: true,
  templateUrl: './nav-header.component.html',
  styleUrl: './nav-header.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class NavHeaderComponent implements OnInit, OnDestroy, OnChanges {
  @Output() logout = new EventEmitter<void>();
  @Output() login = new EventEmitter<void>();
  @Input() userProfile?: UserProfile;
  showProfileMenu = false;
  themeToggleIcon = '';
  themeMenuIcon = '';
  logoSrc = '';

  private observerManager = new ObserverManager();
  private logger = createLogger('NavHeaderComponent');

  constructor(private themeService: ThemeService, private cdr: ChangeDetectorRef) {}

  ngOnInit(): void {
    this.logger.info(' NavHeader component initialized');
    this.updateThemeAssets();
    this.observerManager.createMutationObserver(
      document.body,
      () => this.updateThemeAssets(),
      {
        attributes: true,
        attributeFilter: ['class'],
      }
    );
    this.logUserProfileStatus();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['userProfile']) {
      this.logger.info('User profile changed in NavHeader');
      this.logUserProfileStatus();
      this.cdr.markForCheck();
    }
  }

  ngOnDestroy(): void {
    this.observerManager.cleanup();
  }

  toggleTheme(): void {
    this.themeService.toggleTheme();
    this.updateThemeAssets();
  }

  toggleProfileMenu(): void {
    this.showProfileMenu = !this.showProfileMenu;
    if (this.showProfileMenu) {
      this.logger.info('Profile menu opened');
      this.logUserProfileStatus();
    } else {
      this.logger.info('Profile menu closed');
    }
    this.cdr.markForCheck();
  }

  onLogout(): void {
    this.logger.info('Logout initiated from NavHeader');
    this.logout.emit();
    this.showProfileMenu = false;
    this.cdr.markForCheck();
  }

  onLogin(): void {
    this.logger.info('Login initiated from NavHeader');
    this.login.emit();
  }

  getProfileImage(): string {
    return this.userProfile?.photoUrl || `${AppConstants.AssetsPath}/user-avatar.svg`;
  }

  getDisplayName(): string {
    const displayName = this.userProfile?.displayName || 'User';
    return displayName;
  }

  getEmail(): string {
    const email = this.userProfile?.mail || this.userProfile?.userPrincipalName || 'No email available';
    return email;
  }

  private logUserProfileStatus(): void {
    if (this.userProfile) {
      this.logger.info(' === USER PROFILE IN NAV HEADER ===');
      this.logger.info(` Display Name: ${this.getDisplayName()}`);
      this.logger.info(`Email: ${this.getEmail()}`);
      this.logger.info(` Photo URL: ${this.userProfile.photoUrl || 'Using default avatar'}`);
      this.logger.info('Full Profile Object:', this.userProfile);
      this.logger.info(' === END NAV HEADER PROFILE ===');
    } else {
      this.logger.warn(' No user profile available in NavHeader');
    }
  }

  private updateThemeAssets(): void {
    const currentTheme = this.themeService.getCurrentTheme();
    this.logoSrc = `${AppConstants.AssetsPath}/ascendion-logo-${currentTheme}.svg`;
    this.themeToggleIcon = `${AppConstants.AssetsPath}/theme-toggle-${currentTheme}.svg`;
    this.themeMenuIcon = `${AppConstants.AssetsPath}/menu-${currentTheme}.svg`;
    this.cdr.markForCheck();
  }
}
