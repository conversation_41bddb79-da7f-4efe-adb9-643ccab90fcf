steps:
  - task: NodeTool@0
    inputs:
      versionSpec: "20.x"
    displayName: "Install Node.js"

  - task: Cache@2
    inputs:
      key: 'npm | "$(Agent.OS)" | package-lock.json'
      restoreKeys: |
        npm | "$(Agent.OS)"
      path: $(npm_config_cache)
    displayName: Cache npm packages

  - script: |
      # Create .npmrc with authentication
      echo "registry=https://registry.npmjs.org/" > .npmrc
      echo "@ascendionava:registry=https://pkgs.dev.azure.com/ascendionava/_packaging/ava-platform/npm/registry/" >> .npmrc
      echo "//pkgs.dev.azure.com/ascendionava/_packaging/ava-platform/npm/registry/:_authToken=$(Azure_DevOPS_PAT)" >> .npmrc
      echo "always-auth=true" >> .npmrc
      
      # Set correct permissions
      chmod 600 .npmrc
      
      # Install dependencies with additional flags to handle peer dependency issues
      npm ci --no-audit --legacy-peer-deps
    displayName: "Configure npm and Install Dependencies"
    env:
      NODE_AUTH_TOKEN: $(Azure_DevOPS_PAT) 