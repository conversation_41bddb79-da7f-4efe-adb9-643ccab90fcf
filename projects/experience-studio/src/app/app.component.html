<div class="container-fluid">
  <app-nav-header [userProfile]="userProfile" (logout)="onLogout()" (login)="triggerLogin()"></app-nav-header>

  <!-- Debug Panel (only show in development) -->
  <div *ngIf="showDebugPanel" class="debug-panel p-3 mb-3 border rounded bg-light">
    <h5>Authentication Debug Panel</h5>
    <div class="row">
      <div class="col-md-6">
        <p><strong>Authentication Status:</strong>
          <span [class]="isLoggedIn ? 'text-success' : 'text-danger'">
            {{ isLoggedIn ? 'AUTHENTICATED' : 'NOT AUTHENTICATED' }}
          </span>
        </p>
        <p><strong>User Profile:</strong> {{ userProfile ? 'Loaded' :  Not Available' }}</p>
        <p *ngIf="userProfile"><strong>Display Name:</strong> {{ userProfile.displayName }}</p>
        <p *ngIf="userProfile"><strong>Email:</strong> {{ userProfile.mail || userProfile.userPrincipalName }}</p>
      </div>
      <div class="col-md-6">
        <button class="btn btn-sm btn-primary me-2" (click)="checkAuthenticationStatus()">Check Auth Status</button>
        <button class="btn btn-sm btn-success me-2" (click)="triggerLogin()">Trigger Login</button>
        <button class="btn btn-sm btn-warning me-2" (click)="getTokenInfo()">Get Token Info</button>
        <button class="btn btn-sm btn-danger" (click)="clearTokensAndLogout()">Clear Tokens</button>
      </div>
    </div>
  </div>

  <router-outlet></router-outlet>
</div>
