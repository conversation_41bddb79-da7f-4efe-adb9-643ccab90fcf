// src/app/auth/services/auth-token.service.ts
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { AuthService } from '../auth-service/auth.service';
import { TokenStorageService } from '../token-storage/token-storage.service';

@Injectable({
  providedIn: 'root',
})
export class AuthTokenService {
  private tokenCheckInterval: any;
  private checkIntervalMs = 4000;
  private subscription = new Subscription();

  constructor(
    private router: Router,
    private authService: AuthService,
    private tokenStorageService: TokenStorageService
  ) {}

  public handleAuthCodeAndToken(): void {
    const authCode = new URLSearchParams(window.location.search).get('code');
    const accessToken = this.tokenStorageService.getAccessToken();
    const refreshToken = this.tokenStorageService.getRefreshToken();

    if (!accessToken && refreshToken) {
      this.router.navigateByUrl(`/callback?refresh_token=${refreshToken}`);
    }
    if (authCode && !accessToken && !refreshToken) {
      this.router.navigateByUrl(`/callback?code=${authCode}`);
    }
  }

  public startTokenCheck(): void {
    this.tokenCheckInterval = setInterval(() => {
      const accessToken = this.tokenStorageService.getAccessToken();
      if (!accessToken) {
        const refreshToken = this.tokenStorageService.getRefreshToken();
        if (refreshToken) {
          this.handleTokenRefresh(refreshToken);
        } else {
          this.router.navigateByUrl('/login');
        }
      }
    }, this.checkIntervalMs);
  }

  private handleTokenRefresh(refreshToken: string): void {
    const refreshSub = this.authService.refreshToken(refreshToken).subscribe({
      next: () => {
        console.log('Token refreshed successfully');
      },
      error: err => {
        console.error('Token refresh failed:', err);
        this.router.navigateByUrl('/login');
      },
    });
    this.subscription.add(refreshSub);
  }

  public stopTokenCheck(): void {
    if (this.tokenCheckInterval) {
      clearInterval(this.tokenCheckInterval);
    }
    this.subscription.unsubscribe();
  }
}
