import { NewPollingResponse } from '../models/polling-response.interface';

/**
 * Mock polling responses for different states and scenarios
 * Updated to include comprehensive examples for all progress states and status types
 */

// OVERVIEW State
export const MOCK_OVERVIEW_IN_PROGRESS: NewPollingResponse = {
  progress: "PROJECT_OVERVIEW",
  status: "IN_PROGRESS",
  log: "🔍 Analyzing project requirements and initializing workspace...\n📋 Processing user specifications and technical requirements\n🏗️ Setting up project structure and configuration\n📊 Evaluating project scope and complexity\n⚙️ Configuring development environment settings",
  progress_description: "Analyzing project requirements, setting up workspace, and preparing development environment for E-Commerce Dashboard application",
  history: [],
  metadata: [
    {
      type: "fileNames",
      data: JSON.stringify([
        "project-analysis.md",
        "requirements-specification.md",
        "technical-architecture.md",
        "development-setup.md"
      ])
    },
    {
      type: "files",
      data: JSON.stringify([
        {
          "fileName": "docs/project-analysis.md",
          "code": "# Project Analysis Report\n\n## Initial Assessment\n- Project Type: E-Commerce Dashboard\n- Complexity Level: High\n- Estimated Timeline: 9 weeks\n- Technology Stack: Angular 19, TypeScript, Material Design\n\n## Key Requirements Identified\n1. Real-time analytics dashboard\n2. Order management system\n3. Inventory tracking\n4. User authentication & authorization\n5. Responsive design for all devices\n\n## Technical Considerations\n- State management with NgRx\n- Progressive Web App capabilities\n- Accessibility compliance (WCAG 2.1 AA)\n- Performance optimization\n- Cross-browser compatibility"
        }
      ])
    }
  ]
};

export const MOCK_OVERVIEW_COMPLETED: NewPollingResponse = {
  progress: "PROJECT_OVERVIEW",
  status: "COMPLETED",
  log: "✅ Project overview completed successfully. Requirements analyzed and documented.\n📋 All stakeholder requirements captured\n🎯 Success criteria defined\n📊 Technical architecture finalized",
  progress_description: "Project overview and requirements analysis completed successfully with all stakeholder requirements captured",
  history: [],
  metadata: [
    {
      type: "artifact",
      data: JSON.stringify({
        type: "text",
        value: "# E-Commerce Dashboard Project\n\n## Project Overview\n\nThis project aims to create a modern e-commerce dashboard application with comprehensive analytics and management capabilities.\n\n### Core Features\n- **Responsive Design**: Mobile-first approach with desktop optimization\n- **User Management**: Authentication, authorization, and role-based access\n- **Real-time Analytics**: Live sales data, inventory tracking, and performance metrics\n- **Interactive Dashboard**: Customizable widgets and data visualization\n- **Order Management**: Complete order lifecycle management\n- **Inventory Control**: Stock management and automated alerts\n\n### Technical Requirements\n- **Framework**: Angular 19 with TypeScript\n- **UI Library**: Material Design components with custom theming\n- **State Management**: NgRx for complex state management\n- **API Integration**: RESTful services with real-time WebSocket connections\n- **Progressive Web App**: Offline capabilities and push notifications\n- **Testing**: Unit tests with Jest, E2E tests with Cypress\n\n### Success Criteria\n- Page load time under 2 seconds\n- 98% accessibility compliance (WCAG 2.1 AA)\n- Cross-browser compatibility (Chrome, Firefox, Safari, Edge)\n- Mobile-first responsive design\n- 99.9% uptime reliability\n\n### Project Timeline\n- **Phase 1**: Core dashboard and authentication (4 weeks)\n- **Phase 2**: Analytics and reporting features (3 weeks)\n- **Phase 3**: Advanced features and optimization (2 weeks)\n\n### Key Stakeholders\n- Product Manager: Sarah Johnson\n- Lead Developer: Michael Chen\n- UX Designer: Emily Rodriguez\n- QA Lead: David Kim"
      })
    },
    {
      type: "ref_code",
      data: "ecommerce-dashboard-2024"
    },
    {
      type: "fileNames",
      data: JSON.stringify([
        "project-overview.md",
        "requirements-specification.md",
        "technical-architecture.md",
        "stakeholder-analysis.md"
      ])
    }
  ]
};

// SEED_PROJECT_INITIALIZED State
export const MOCK_SEED_PROJECT_INITIALIZED_IN_PROGRESS: NewPollingResponse = {
  progress: "SEED_PROJECT_INITIALIZED",
  status: "IN_PROGRESS",
  log: "🌱 Initializing seed project structure...\n📁 Creating base directory structure\n⚙️ Setting up configuration files\n📦 Installing core dependencies\n🔧 Configuring build tools and development environment",
  progress_description: "Setting up initial project structure with core dependencies and build configuration",
  history: [
    {
      progress: "OVERVIEW",
      status: "COMPLETED",
      log: "Project overview completed successfully.",
      progress_description: "Project overview and requirements analysis completed",
      metadata: []
    }
  ],
  metadata: [
    {
      type: "fileNames",
      data: JSON.stringify([
        "package.json",
        "angular.json",
        "tsconfig.json",
        "src/main.ts",
        "src/index.html"
      ])
    },
    {
      type: "files",
      data: JSON.stringify([
        {
          "fileName": "package.json",
          "code": "{\n  \"name\": \"ecommerce-dashboard\",\n  \"version\": \"1.0.0\",\n  \"scripts\": {\n    \"ng\": \"ng\",\n    \"start\": \"ng serve\",\n    \"build\": \"ng build\",\n    \"test\": \"ng test\"\n  },\n  \"dependencies\": {\n    \"@angular/core\": \"^19.0.0\",\n    \"@angular/common\": \"^19.0.0\",\n    \"@angular/router\": \"^19.0.0\",\n    \"@angular/material\": \"^19.0.0\",\n    \"rxjs\": \"^7.8.0\",\n    \"typescript\": \"^5.0.0\"\n  }\n}"
        }
      ])
    }
  ]
};

export const MOCK_SEED_PROJECT_INITIALIZED_COMPLETED: NewPollingResponse = {
  progress: "SEED_PROJECT_INITIALIZED",
  status: "COMPLETED",
  log: "✅ Seed project initialized successfully.\n📁 Project structure created\n📦 Dependencies installed\n⚙️ Configuration files set up\n🔧 Development environment ready",
  progress_description: "Seed project initialization completed with all core dependencies and configuration files in place",
  history: [
    {
      progress: "OVERVIEW",
      status: "COMPLETED",
      log: "Project overview completed successfully.",
      progress_description: "Project overview and requirements analysis completed",
      metadata: []
    }
  ],
  metadata: [
    {
      type: "fileNames",
      data: JSON.stringify([
        "package.json",
        "angular.json",
        "tsconfig.json",
        "src/main.ts",
        "src/index.html",
        "src/app/app.component.ts",
        "src/styles.scss"
      ])
    }
  ]
};

// FILE_QUEUE State
export const MOCK_FILE_QUEUE_IN_PROGRESS: NewPollingResponse = {
  progress: "FILE_QUEUE",
  status: "IN_PROGRESS",
  log: "📋 Identifying and queuing components for generation...\n🔍 Analyzing UI requirements and component dependencies\n📝 Creating component specification queue\n🧩 Mapping component relationships and hierarchy\n⚡ Optimizing component generation order",
  progress_description: "Analyzing UI requirements and creating optimized queue for component generation",
  history: [
    {
      progress: "OVERVIEW",
      status: "COMPLETED",
      log: "Project overview completed successfully.",
      progress_description: "Project overview and requirements analysis completed",
      metadata: []
    },
    {
      progress: "SEED_PROJECT_INITIALIZED",
      status: "COMPLETED",
      log: "Seed project initialized successfully.",
      progress_description: "Seed project initialization completed",
      metadata: []
    }
  ],
  metadata: [
    {
      type: "fileNames",
      data: JSON.stringify([
        "component-queue.json",
        "dependency-map.json",
        "generation-plan.md"
      ])
    },
    {
      type: "files",
      data: JSON.stringify([
        {
          "fileName": "component-queue.json",
          "code": "{\n  \"totalComponents\": 12,\n  \"queue\": [\n    {\n      \"name\": \"HeaderComponent\",\n      \"priority\": 1,\n      \"dependencies\": [\"SearchBarComponent\", \"UserProfileComponent\"]\n    },\n    {\n      \"name\": \"SidebarComponent\",\n      \"priority\": 2,\n      \"dependencies\": [\"NavigationMenuComponent\"]\n    },\n    {\n      \"name\": \"DashboardComponent\",\n      \"priority\": 3,\n      \"dependencies\": [\"AnalyticsWidgetComponent\", \"SalesChartComponent\"]\n    }\n  ]\n}"
        }
      ])
    }
  ]
};

export const MOCK_FILE_QUEUE_COMPLETED: NewPollingResponse = {
  progress: "FILE_QUEUE",
  status: "COMPLETED",
  log: "✅ Component queue created successfully.\n📋 12 components identified and prioritized\n🧩 Component dependencies mapped\n⚡ Generation order optimized\n📝 Component specifications ready",
  progress_description: "Component identification and queue creation completed with optimized generation plan",
  history: [
    {
      progress: "OVERVIEW",
      status: "COMPLETED",
      log: "Project overview completed successfully.",
      progress_description: "Project overview and requirements analysis completed",
      metadata: []
    },
    {
      progress: "SEED_PROJECT_INITIALIZED",
      status: "COMPLETED",
      log: "Seed project initialized successfully.",
      progress_description: "Seed project initialization completed",
      metadata: []
    }
  ],
  metadata: [
    {
      type: "fileNames",
      data: JSON.stringify([
        "component-queue.json",
        "dependency-map.json",
        "generation-plan.md",
        "component-specifications.json"
      ])
    }
  ]
};

// DESIGN_SYSTEM_MAPPED State
export const MOCK_DESIGN_SYSTEM_MAPPED_IN_PROGRESS: NewPollingResponse = {
  progress: "DESIGN_SYSTEM_MAPPED",
  status: "IN_PROGRESS",
  log: "🎨 Mapping design system and extracting design tokens...\n🌈 Processing color palette and brand guidelines\n📝 Analyzing typography hierarchy and font systems\n🔘 Identifying button styles and interactive elements\n⚡ Creating design token specifications",
  progress_description: "Analyzing design system, extracting design tokens, and creating comprehensive style guide",
  history: [
    {
      progress: "OVERVIEW",
      status: "COMPLETED",
      log: "Project overview completed successfully.",
      progress_description: "Project overview and requirements analysis completed",
      metadata: []
    },
    {
      progress: "SEED_PROJECT_INITIALIZED",
      status: "COMPLETED",
      log: "Seed project initialized successfully.",
      progress_description: "Seed project initialization completed",
      metadata: []
    },
    {
      progress: "FILE_QUEUE",
      status: "COMPLETED",
      log: "Component queue created successfully.",
      progress_description: "Component identification and queue creation completed",
      metadata: []
    }
  ],
  metadata: [
    {
      type: "fileNames",
      data: JSON.stringify([
        "design-tokens.json",
        "color-palette.scss",
        "typography-system.scss",
        "component-styles.scss"
      ])
    },
    {
      type: "files",
      data: JSON.stringify([
        {
          "fileName": "design-tokens.json",
          "code": "{\n  \"colors\": {\n    \"primary\": \"#2196f3\",\n    \"secondary\": \"#e91e63\",\n    \"success\": \"#4caf50\",\n    \"warning\": \"#ff9800\",\n    \"error\": \"#f44336\"\n  },\n  \"typography\": {\n    \"fontFamily\": \"Roboto, sans-serif\",\n    \"fontSize\": {\n      \"small\": \"0.875rem\",\n      \"medium\": \"1rem\",\n      \"large\": \"1.25rem\"\n    }\n  }\n}"
        }
      ])
    }
  ]
};

export const MOCK_DESIGN_SYSTEM_MAPPED_COMPLETED: NewPollingResponse = {
  progress: "DESIGN_SYSTEM_MAPPED",
  status: "COMPLETED",
  log: "✅ Design system mapping completed successfully.\n🎨 Design tokens extracted and validated\n📝 Typography system established\n🔘 Button styles and variants defined\n🌈 Color palette optimized",
  progress_description: "Design system analysis and token extraction completed with comprehensive style guide established",
  history: [
    {
      progress: "OVERVIEW",
      status: "COMPLETED",
      log: "Project overview completed successfully.",
      progress_description: "Project overview and requirements analysis completed",
      metadata: []
    },
    {
      progress: "SEED_PROJECT_INITIALIZED",
      status: "COMPLETED",
      log: "Seed project initialized successfully.",
      progress_description: "Seed project initialization completed",
      metadata: []
    },
    {
      progress: "FILE_QUEUE",
      status: "COMPLETED",
      log: "Component queue created successfully.",
      progress_description: "Component identification and queue creation completed",
      metadata: []
    }
  ],
  metadata: [
    {
      type: "artifact",
      data: JSON.stringify({
        type: "json",
        value: {
          colors: [
            { name: "Primary Blue", value: "#2196f3", type: "primary", enabled: true },
            { name: "Secondary Pink", value: "#e91e63", type: "secondary", enabled: true },
            { name: "Success Green", value: "#4caf50", type: "success", enabled: true },
            { name: "Warning Orange", value: "#ff9800", type: "warning", enabled: true },
            { name: "Error Red", value: "#f44336", type: "error", enabled: true },
            { name: "Background Light", value: "#fafafa", type: "background", enabled: true },
            { name: "Text Primary", value: "#212121", type: "text", enabled: true },
            { name: "Text Secondary", value: "#757575", type: "text", enabled: true }
          ],
          fonts: [
            { name: "Roboto", type: "primary", enabled: true },
            { name: "Roboto Mono", type: "monospace", enabled: true }
          ],
          buttons: [
            { name: "Primary Button", type: "primary", enabled: true },
            { name: "Secondary Button", type: "secondary", enabled: true },
            { name: "Outlined Button", type: "outlined", enabled: true }
          ]
        }
      })
    },
    {
      type: "fileNames",
      data: JSON.stringify([
        "design-tokens.json",
        "color-palette.scss",
        "typography-system.scss",
        "button-styles.scss",
        "component-library.scss"
      ])
    }
  ]
};

// LAYOUT_ANALYZED State
export const MOCK_LAYOUT_ANALYZED_IN_PROGRESS: NewPollingResponse = {
  progress: "LAYOUT_ANALYZED",
  status: "IN_PROGRESS",
  log: "🎨 Processing layout structure and identifying UI components...\n📐 Analyzing responsive design patterns and breakpoints\n🧩 Mapping component relationships and dependencies\n📱 Evaluating mobile-first design approach\n🔍 Identifying reusable component patterns\n⚡ Optimizing layout for performance and accessibility",
  progress_description: "Analyzing layout structure, identifying UI components, and mapping responsive design patterns for optimal user experience",
  history: [
    {
      progress: "OVERVIEW",
      status: "COMPLETED",
      log: "Project overview completed successfully.",
      progress_description: "Project overview and requirements analysis completed",
      metadata: []
    },
    {
      progress: "SEED_PROJECT_INITIALIZED",
      status: "COMPLETED",
      log: "Seed project initialized successfully.",
      progress_description: "Seed project initialization completed",
      metadata: []
    },
    {
      progress: "FILE_QUEUE",
      status: "COMPLETED",
      log: "Component queue created successfully.",
      progress_description: "Component identification and queue creation completed",
      metadata: []
    },
    {
      progress: "DESIGN_SYSTEM_MAPPED",
      status: "COMPLETED",
      log: "Design system mapping completed successfully.",
      progress_description: "Design system analysis and token extraction completed",
      metadata: []
    }
  ],
  metadata: [
    {
      type: "fileNames",
      data: JSON.stringify([
        "layout-analysis.md",
        "component-hierarchy.json",
        "responsive-breakpoints.scss",
        "accessibility-audit.md"
      ])
    },
    {
      type: "files",
      data: JSON.stringify([
        {
          "fileName": "analysis/layout-analysis.md",
          "code": "# Layout Analysis Report\n\n## Layout Structure Identified\n- **Header**: Navigation, search, user profile\n- **Sidebar**: Main navigation menu, quick actions\n- **Main Content**: Dashboard widgets, data tables\n- **Footer**: Links, copyright, version info\n\n## Component Breakdown\n### Header Components (4 identified)\n- AppHeaderComponent\n- SearchBarComponent\n- NotificationPanelComponent\n- UserProfileComponent\n\n### Sidebar Components (3 identified)\n- SidebarNavigationComponent\n- QuickActionsComponent\n- UserMenuComponent\n\n### Main Content Components (5 identified)\n- DashboardWidgetComponent\n- AnalyticsChartComponent\n- DataTableComponent\n- FilterPanelComponent\n- ActionButtonsComponent\n\n## Responsive Design Analysis\n- Mobile breakpoint: 768px\n- Tablet breakpoint: 1024px\n- Desktop breakpoint: 1200px\n- Large screen: 1440px+"
        }
      ])
    }
  ]
};

export const MOCK_LAYOUT_ANALYZED_COMPLETED: NewPollingResponse = {
  progress: "LAYOUT_ANALYZED",
  status: "COMPLETED",
  log: "✅ Layout analysis completed successfully. Identified 12 components and 3 main sections.\n🎨 Layout structure mapped and optimized\n📱 Responsive breakpoints configured\n🧩 Component hierarchy established",
  progress_description: "Layout structure analysis and component identification completed with responsive design patterns established",
  history: [
    {
      progress: "OVERVIEW",
      status: "COMPLETED",
      log: "Project overview completed successfully.",
      progress_description: "Project overview and requirements analysis completed",
      metadata: []
    },
    {
      progress: "SEED_PROJECT_INITIALIZED",
      status: "COMPLETED",
      log: "Seed project initialized successfully.",
      progress_description: "Seed project initialization completed",
      metadata: []
    },
    {
      progress: "FILE_QUEUE",
      status: "COMPLETED",
      log: "Component queue created successfully.",
      progress_description: "Component identification and queue creation completed",
      metadata: []
    },
    {
      progress: "DESIGN_SYSTEM_MAPPED",
      status: "COMPLETED",
      log: "Design system mapping completed successfully.",
      progress_description: "Design system analysis and token extraction completed",
      metadata: []
    }
  ],
  metadata: [
    {
      type: "artifact",
      data: JSON.stringify({
        type: "string",
        value: "HB-HF-SB-SF-CB-CF"
      })
    },
    {
      type: "fileNames",
      data: JSON.stringify([
        "dashboard.component.ts",
        "header.component.ts",
        "sidebar.component.ts",
        "main-content.component.ts",
        "footer.component.ts",
        "analytics-widget.component.ts",
        "sales-chart.component.ts",
        "inventory-table.component.ts",
        "user-profile.component.ts",
        "notification-panel.component.ts",
        "search-bar.component.ts",
        "navigation-menu.component.ts"
      ])
    }
  ]
};

// COMPONENTS_CREATED State
export const MOCK_COMPONENTS_CREATED_IN_PROGRESS: NewPollingResponse = {
  progress: "COMPONENTS_CREATED",
  status: "IN_PROGRESS",
  log: "🧩 Creating components based on queue specifications...\n⚡ Generating TypeScript component files\n🎨 Applying design system tokens to components\n📝 Creating component templates and styles\n🔧 Setting up component dependencies and imports",
  progress_description: "Generating Angular components with TypeScript, templates, and styles based on design specifications",
  history: [
    {
      progress: "OVERVIEW",
      status: "COMPLETED",
      log: "Project overview completed successfully.",
      progress_description: "Project overview and requirements analysis completed",
      metadata: []
    },
    {
      progress: "SEED_PROJECT_INITIALIZED",
      status: "COMPLETED",
      log: "Seed project initialized successfully.",
      progress_description: "Seed project initialization completed",
      metadata: []
    },
    {
      progress: "FILE_QUEUE",
      status: "COMPLETED",
      log: "Component queue created successfully.",
      progress_description: "Component identification and queue creation completed",
      metadata: []
    },
    {
      progress: "DESIGN_SYSTEM_MAPPED",
      status: "COMPLETED",
      log: "Design system mapping completed successfully.",
      progress_description: "Design system analysis and token extraction completed",
      metadata: []
    },
    {
      progress: "LAYOUT_ANALYZED",
      status: "COMPLETED",
      log: "Layout analysis completed successfully.",
      progress_description: "Layout structure analysis and component identification completed",
      metadata: []
    }
  ],
  metadata: [
    {
      type: "fileNames",
      data: JSON.stringify([
        "header.component.ts",
        "header.component.scss",
        "sidebar.component.ts",
        "sidebar.component.scss",
        "dashboard.component.ts",
        "dashboard.component.scss"
      ])
    },
    {
      type: "files",
      data: JSON.stringify([
        {
          "fileName": "src/app/components/header/header.component.ts",
          "code": "import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\n@Component({\n  selector: 'app-header',\n  standalone: true,\n  imports: [CommonModule],\n  template: `\n    <header class=\"app-header\">\n      <div class=\"header-content\">\n        <div class=\"logo-section\">\n          <h1>E-Commerce Dashboard</h1>\n        </div>\n        <div class=\"header-actions\">\n          <!-- Actions will be added here -->\n        </div>\n      </div>\n    </header>\n  `,\n  styleUrls: ['./header.component.scss']\n})\nexport class HeaderComponent {}"
        }
      ])
    }
  ]
};

export const MOCK_COMPONENTS_CREATED_COMPLETED: NewPollingResponse = {
  progress: "COMPONENTS_CREATED",
  status: "COMPLETED",
  log: "✅ Components created successfully.\n🧩 12 Angular components generated\n🎨 Design system tokens applied\n📝 Templates and styles implemented\n🔧 Component dependencies configured",
  progress_description: "Component generation completed with all Angular components, templates, and styles created",
  history: [
    {
      progress: "OVERVIEW",
      status: "COMPLETED",
      log: "Project overview completed successfully.",
      progress_description: "Project overview and requirements analysis completed",
      metadata: []
    },
    {
      progress: "SEED_PROJECT_INITIALIZED",
      status: "COMPLETED",
      log: "Seed project initialized successfully.",
      progress_description: "Seed project initialization completed",
      metadata: []
    },
    {
      progress: "FILE_QUEUE",
      status: "COMPLETED",
      log: "Component queue created successfully.",
      progress_description: "Component identification and queue creation completed",
      metadata: []
    },
    {
      progress: "DESIGN_SYSTEM_MAPPED",
      status: "COMPLETED",
      log: "Design system mapping completed successfully.",
      progress_description: "Design system analysis and token extraction completed",
      metadata: []
    },
    {
      progress: "LAYOUT_ANALYZED",
      status: "COMPLETED",
      log: "Layout analysis completed successfully.",
      progress_description: "Layout structure analysis and component identification completed",
      metadata: []
    }
  ],
  metadata: [
    {
      type: "fileNames",
      data: JSON.stringify([
        "header.component.ts",
        "header.component.scss",
        "sidebar.component.ts",
        "sidebar.component.scss",
        "dashboard.component.ts",
        "dashboard.component.scss",
        "analytics-widget.component.ts",
        "sales-chart.component.ts",
        "inventory-table.component.ts",
        "user-profile.component.ts",
        "notification-panel.component.ts",
        "search-bar.component.ts"
      ])
    }
  ]
};

// PAGES_GENERATED State
export const MOCK_PAGES_GENERATED_IN_PROGRESS: NewPollingResponse = {
  progress: "PAGES_GENERATED",
  status: "IN_PROGRESS",
  log: "📄 Generating application pages and routing...\n🔗 Setting up Angular routing configuration\n📱 Creating responsive page layouts\n🧩 Integrating components into pages\n⚡ Optimizing page load performance",
  progress_description: "Creating application pages with routing, integrating components, and optimizing for performance",
  history: [
    {
      progress: "OVERVIEW",
      status: "COMPLETED",
      log: "Project overview completed successfully.",
      progress_description: "Project overview and requirements analysis completed",
      metadata: []
    },
    {
      progress: "SEED_PROJECT_INITIALIZED",
      status: "COMPLETED",
      log: "Seed project initialized successfully.",
      progress_description: "Seed project initialization completed",
      metadata: []
    },
    {
      progress: "FILE_QUEUE",
      status: "COMPLETED",
      log: "Component queue created successfully.",
      progress_description: "Component identification and queue creation completed",
      metadata: []
    },
    {
      progress: "DESIGN_SYSTEM_MAPPED",
      status: "COMPLETED",
      log: "Design system mapping completed successfully.",
      progress_description: "Design system analysis and token extraction completed",
      metadata: []
    },
    {
      progress: "LAYOUT_ANALYZED",
      status: "COMPLETED",
      log: "Layout analysis completed successfully.",
      progress_description: "Layout structure analysis and component identification completed",
      metadata: []
    },
    {
      progress: "COMPONENTS_CREATED",
      status: "COMPLETED",
      log: "Components created successfully.",
      progress_description: "Component generation completed",
      metadata: []
    }
  ],
  metadata: [
    {
      type: "fileNames",
      data: JSON.stringify([
        "app-routing.module.ts",
        "dashboard.page.ts",
        "analytics.page.ts",
        "orders.page.ts",
        "inventory.page.ts",
        "settings.page.ts"
      ])
    },
    {
      type: "files",
      data: JSON.stringify([
        {
          "fileName": "src/app/app-routing.module.ts",
          "code": "import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { DashboardPageComponent } from './pages/dashboard/dashboard.page';\nimport { AnalyticsPageComponent } from './pages/analytics/analytics.page';\nimport { OrdersPageComponent } from './pages/orders/orders.page';\n\nconst routes: Routes = [\n  { path: '', redirectTo: '/dashboard', pathMatch: 'full' },\n  { path: 'dashboard', component: DashboardPageComponent },\n  { path: 'analytics', component: AnalyticsPageComponent },\n  { path: 'orders', component: OrdersPageComponent },\n  { path: '**', redirectTo: '/dashboard' }\n];\n\n@NgModule({\n  imports: [RouterModule.forRoot(routes)],\n  exports: [RouterModule]\n})\nexport class AppRoutingModule { }"
        }
      ])
    }
  ]
};

export const MOCK_PAGES_GENERATED_COMPLETED: NewPollingResponse = {
  progress: "PAGES_GENERATED",
  status: "COMPLETED",
  log: "✅ Pages generated successfully.\n📄 5 application pages created\n🔗 Routing configuration implemented\n📱 Responsive layouts applied\n🧩 Components integrated into pages",
  progress_description: "Page generation completed with routing, responsive layouts, and component integration",
  history: [
    {
      progress: "OVERVIEW",
      status: "COMPLETED",
      log: "Project overview completed successfully.",
      progress_description: "Project overview and requirements analysis completed",
      metadata: []
    },
    {
      progress: "SEED_PROJECT_INITIALIZED",
      status: "COMPLETED",
      log: "Seed project initialized successfully.",
      progress_description: "Seed project initialization completed",
      metadata: []
    },
    {
      progress: "FILE_QUEUE",
      status: "COMPLETED",
      log: "Component queue created successfully.",
      progress_description: "Component identification and queue creation completed",
      metadata: []
    },
    {
      progress: "DESIGN_SYSTEM_MAPPED",
      status: "COMPLETED",
      log: "Design system mapping completed successfully.",
      progress_description: "Design system analysis and token extraction completed",
      metadata: []
    },
    {
      progress: "LAYOUT_ANALYZED",
      status: "COMPLETED",
      log: "Layout analysis completed successfully.",
      progress_description: "Layout structure analysis and component identification completed",
      metadata: []
    },
    {
      progress: "COMPONENTS_CREATED",
      status: "COMPLETED",
      log: "Components created successfully.",
      progress_description: "Component generation completed",
      metadata: []
    }
  ],
  metadata: [
    {
      type: "fileNames",
      data: JSON.stringify([
        "app-routing.module.ts",
        "dashboard.page.ts",
        "analytics.page.ts",
        "orders.page.ts",
        "inventory.page.ts",
        "settings.page.ts",
        "dashboard.page.scss",
        "analytics.page.scss",
        "orders.page.scss"
      ])
    }
  ]
};

// BUILD_STARTED State
export const MOCK_BUILD_STARTED_IN_PROGRESS: NewPollingResponse = {
  progress: "BUILD_STARTED",
  status: "IN_PROGRESS",
  log: "🚀 Starting production build process...\n📦 Compiling TypeScript and bundling assets\n🔧 Optimizing build for production deployment\n⚡ Running build optimizations and tree shaking\n🔒 Applying security configurations",
  progress_description: "Building production-ready application with optimizations, bundling, and security configurations",
  history: [
    {
      progress: "OVERVIEW",
      status: "COMPLETED",
      log: "Project overview completed successfully.",
      progress_description: "Project overview and requirements analysis completed",
      metadata: []
    },
    {
      progress: "SEED_PROJECT_INITIALIZED",
      status: "COMPLETED",
      log: "Seed project initialized successfully.",
      progress_description: "Seed project initialization completed",
      metadata: []
    },
    {
      progress: "FILE_QUEUE",
      status: "COMPLETED",
      log: "Component queue created successfully.",
      progress_description: "Component identification and queue creation completed",
      metadata: []
    },
    {
      progress: "DESIGN_SYSTEM_MAPPED",
      status: "COMPLETED",
      log: "Design system mapping completed successfully.",
      progress_description: "Design system analysis and token extraction completed",
      metadata: []
    },
    {
      progress: "LAYOUT_ANALYZED",
      status: "COMPLETED",
      log: "Layout analysis completed successfully.",
      progress_description: "Layout structure analysis and component identification completed",
      metadata: []
    },
    {
      progress: "COMPONENTS_CREATED",
      status: "COMPLETED",
      log: "Components created successfully.",
      progress_description: "Component generation completed",
      metadata: []
    },
    {
      progress: "PAGES_GENERATED",
      status: "COMPLETED",
      log: "Pages generated successfully.",
      progress_description: "Page generation completed",
      metadata: []
    }
  ],
  metadata: [
    {
      type: "fileNames",
      data: JSON.stringify([
        "build-config.json",
        "webpack.config.js",
        "optimization-report.md"
      ])
    },
    {
      type: "files",
      data: JSON.stringify([
        {
          "fileName": "build-config.json",
          "code": "{\n  \"buildVersion\": \"1.0.0\",\n  \"buildTime\": \"2024-01-15T10:30:00Z\",\n  \"environment\": \"production\",\n  \"optimization\": {\n    \"minification\": true,\n    \"treeshaking\": true,\n    \"bundleAnalysis\": true\n  },\n  \"performance\": {\n    \"targetSize\": \"2MB\",\n    \"loadTime\": \"<2s\"\n  }\n}"
        }
      ])
    }
  ]
};

// Legacy LAYOUT_ANALYZED entries for backward compatibility
export const MOCK_LAYOUT_ANALYZED_LEGACY_IN_PROGRESS: NewPollingResponse = {
  progress: "Layout Analyzed",
  status: "IN_PROGRESS",
  log: "🎨 Processing layout structure and identifying UI components...\n📐 Analyzing responsive design patterns and breakpoints\n🧩 Mapping component relationships and dependencies\n📱 Evaluating mobile-first design approach\n🔍 Identifying reusable component patterns\n⚡ Optimizing layout for performance and accessibility",
  progress_description: "Analyzing layout structure, identifying UI components, and mapping responsive design patterns for optimal user experience",
  history: [
    {
      progress: "project-overview",
      status: "COMPLETED",
      log: "Project overview completed successfully.",
      progress_description: "Project overview and requirements analysis completed",
      metadata: [
        {
          type: "files",
          data: JSON.stringify([
            {
              "fileName": "project-overview.md"
            }
          ])
        }
      ]
    }
  ],
  metadata: [
    {
      type: "fileNames",
      data: JSON.stringify([
        "layout-analysis.md",
        "component-hierarchy.json",
        "responsive-breakpoints.scss",
        "accessibility-audit.md"
      ])
    },
    {
      type: "files",
      data: JSON.stringify([
        {
          "fileName": "analysis/layout-analysis.md",
          "code": "# Layout Analysis Report\n\n## Layout Structure Identified\n- **Header**: Navigation, search, user profile\n- **Sidebar**: Main navigation menu, quick actions\n- **Main Content**: Dashboard widgets, data tables\n- **Footer**: Links, copyright, version info\n\n## Component Breakdown\n### Header Components (4 identified)\n- AppHeaderComponent\n- SearchBarComponent\n- NotificationPanelComponent\n- UserProfileComponent\n\n### Sidebar Components (3 identified)\n- SidebarNavigationComponent\n- QuickActionsComponent\n- UserMenuComponent\n\n### Main Content Components (5 identified)\n- DashboardWidgetComponent\n- AnalyticsChartComponent\n- DataTableComponent\n- FilterPanelComponent\n- ActionButtonsComponent\n\n## Responsive Design Analysis\n- Mobile breakpoint: 768px\n- Tablet breakpoint: 1024px\n- Desktop breakpoint: 1200px\n- Large screen: 1440px+"
        }
      ])
    }
  ]
};

export const MOCK_LAYOUT_ANALYZED_LEGACY_COMPLETED: NewPollingResponse = {
  progress: "Layout Analyzed",
  status: "COMPLETED",
  log: "✅ Layout analysis completed successfully. Identified 12 components and 3 main sections.\n🎨 Layout structure mapped and optimized\n📱 Responsive breakpoints configured\n🧩 Component hierarchy established",
  progress_description: "Layout structure analysis and component identification completed with responsive design patterns established",
  history: [
    {
      progress: "project-overview",
      status: "COMPLETED",
      log: "Project overview completed successfully.",
      progress_description: "Project overview and requirements analysis completed",
      metadata: [
        {
          type: "files",
          data: JSON.stringify([
            {
              "fileName": "project-overview.md"
            }
          ])
        }
      ]
    },
    {
      progress: "Layout Analyzed",
      status: "IN_PROGRESS",
      log: "Processing layout structure...",
      progress_description: "Analyzing layout structure",
      metadata: [
        {
          type: "files",
          data: JSON.stringify([
            {
              "fileName": "layout-analysis.md"
            }
          ])
        }
      ]
    }
  ],
  metadata: [
    {
      type: "artifact",
      data: JSON.stringify({
        type: "string",
        value: "HB-HF-SB-SF-CB-CF"
      })
    },
    {
      type: "fileNames",
      data: JSON.stringify([
        "dashboard.component.ts",
        "header.component.ts",
        "sidebar.component.ts",
        "main-content.component.ts",
        "footer.component.ts",
        "analytics-widget.component.ts",
        "sales-chart.component.ts",
        "inventory-table.component.ts",
        "user-profile.component.ts",
        "notification-panel.component.ts",
        "search-bar.component.ts",
        "navigation-menu.component.ts"
      ])
    },
    {
      type: "files",
      data: JSON.stringify([
        {
          "fileName": "src/app/components/dashboard/dashboard.component.ts",
          "code": "import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\n@Component({\n  selector: 'app-dashboard',\n  standalone: true,\n  imports: [CommonModule],\n  template: `\n    <div class=\"dashboard-container\">\n      <app-header></app-header>\n      <div class=\"dashboard-content\">\n        <app-sidebar></app-sidebar>\n        <app-main-content></app-main-content>\n      </div>\n      <app-footer></app-footer>\n    </div>\n  `,\n  styleUrls: ['./dashboard.component.scss']\n})\nexport class DashboardComponent implements OnInit {\n  ngOnInit(): void {\n    console.log('Dashboard component initialized');\n  }\n}"
        },
        {
          "fileName": "src/app/components/header/header.component.ts",
          "code": "import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\n@Component({\n  selector: 'app-header',\n  standalone: true,\n  imports: [CommonModule],\n  template: `\n    <header class=\"app-header\">\n      <div class=\"header-content\">\n        <div class=\"logo-section\">\n          <h1>E-Commerce Dashboard</h1>\n        </div>\n        <div class=\"header-actions\">\n          <app-search-bar></app-search-bar>\n          <app-notification-panel></app-notification-panel>\n          <app-user-profile></app-user-profile>\n        </div>\n      </div>\n    </header>\n  `,\n  styleUrls: ['./header.component.scss']\n})\nexport class HeaderComponent {}"
        }
      ])
    }
  ]
};

export const MOCK_DESIGN_SYSTEM_IN_PROGRESS: NewPollingResponse = {
  progress: "Design_System Analyzed",
  status: "IN_PROGRESS",
  log: "🎨 Extracting design tokens and analyzing visual patterns...\n🌈 Processing color palette and brand guidelines\n📝 Analyzing typography hierarchy and font systems\n🔘 Identifying button styles and interactive elements\n📏 Extracting spacing and sizing tokens\n✨ Creating consistent design system documentation",
  progress_description: "Analyzing design system, extracting design tokens, and creating comprehensive style guide for consistent UI development",
  history: [
    {
      progress: "project-overview",
      status: "COMPLETED",
      log: "Project overview completed successfully.",
      progress_description: "Project overview completed",
      metadata: []
    },
    {
      progress: "Layout Analyzed",
      status: "COMPLETED",
      log: "Layout analysis completed.",
      progress_description: "Layout analysis completed",
      metadata: []
    }
  ],
  metadata: [
    {
      type: "fileNames",
      data: JSON.stringify([
        "design-tokens.json",
        "color-palette.scss",
        "typography-system.scss",
        "component-styles.scss",
        "design-system-guide.md"
      ])
    },
    {
      type: "files",
      data: JSON.stringify([
        {
          "fileName": "design-system/design-tokens.json",
          "code": "{\n  \"colors\": {\n    \"primary\": {\n      \"50\": \"#e3f2fd\",\n      \"100\": \"#bbdefb\",\n      \"500\": \"#2196f3\",\n      \"900\": \"#0d47a1\"\n    },\n    \"secondary\": {\n      \"50\": \"#fce4ec\",\n      \"500\": \"#e91e63\",\n      \"900\": \"#880e4f\"\n    }\n  },\n  \"typography\": {\n    \"fontFamily\": \"'Roboto', sans-serif\",\n    \"fontSize\": {\n      \"xs\": \"0.75rem\",\n      \"sm\": \"0.875rem\",\n      \"base\": \"1rem\",\n      \"lg\": \"1.125rem\",\n      \"xl\": \"1.25rem\",\n      \"2xl\": \"1.5rem\"\n    }\n  },\n  \"spacing\": {\n    \"1\": \"0.25rem\",\n    \"2\": \"0.5rem\",\n    \"4\": \"1rem\",\n    \"8\": \"2rem\"\n  }\n}"
        }
      ])
    }
  ]
};

export const MOCK_DESIGN_SYSTEM_COMPLETED: NewPollingResponse = {
  progress: "Design_System Analyzed",
  status: "COMPLETED",
  log: "Design system analysis completed. Extracted 24 color tokens, 8 typography styles, and 12 component variants.",
  progress_description: "Design system analysis and token extraction completed",
  history: [
    {
      progress: "project-overview",
      status: "COMPLETED",
      log: "Project overview completed successfully.",
      progress_description: "Project overview completed",
      metadata: []
    },
    {
      progress: "Layout Analyzed",
      status: "COMPLETED",
      log: "Layout analysis completed.",
      progress_description: "Layout analysis completed",
      metadata: []
    },
    {
      progress: "Design_System Analyzed",
      status: "IN_PROGRESS",
      log: "Extracting design tokens...",
      progress_description: "Analyzing design system",
      metadata: []
    }
  ],
  metadata: [
    {
      type: "artifact",
      data: JSON.stringify({
        type: "json",
        value: {
          colors: [
            { value: "#007bff", name: "Primary Blue", type: "primary", enabled: true },
            { value: "#6c757d", name: "Secondary Gray", type: "secondary", enabled: true },
            { value: "#28a745", name: "Success Green", type: "success", enabled: true },
            { value: "#dc3545", name: "Danger Red", type: "danger", enabled: true },
            { value: "#ffc107", name: "Warning Yellow", type: "warning", enabled: true },
            { value: "#17a2b8", name: "Info Cyan", type: "info", enabled: true }
          ],
          fonts: [
            { name: "Heading 1", size: "2.5rem", weight: "700", lineHeight: "1.2" },
            { name: "Heading 2", size: "2rem", weight: "600", lineHeight: "1.3" },
            { name: "Body Text", size: "1rem", weight: "400", lineHeight: "1.5" },
            { name: "Caption", size: "0.875rem", weight: "400", lineHeight: "1.4" }
          ],
          buttons: [
            { name: "Primary Button", variant: "primary", size: "medium", style: { borderRadius: "4px", padding: "8px 16px" } },
            { name: "Secondary Button", variant: "secondary", size: "medium", style: { borderRadius: "4px", padding: "8px 16px" } },
            { name: "Small Button", variant: "primary", size: "small", style: { borderRadius: "4px", padding: "4px 8px" } }
          ]
        }
      })
    }
  ]
};

// FAILED state examples
export const MOCK_PROJECT_OVERVIEW_FAILED: NewPollingResponse = {
  progress: "project-overview",
  status: "FAILED",
  log: "Failed to analyze project requirements. Invalid project structure detected.",
  progress_description: "Project overview analysis failed",
  history: [
    {
      progress: "project-overview",
      status: "IN_PROGRESS",
      log: "Starting project analysis...",
      progress_description: "Initializing project overview",
      metadata: []
    }
  ],
  metadata: [
    {
      type: "fileNames",
      data: JSON.stringify(["error.log", "debug.txt"])
    }
  ]
};

export const MOCK_LAYOUT_ANALYZED_FAILED: NewPollingResponse = {
  progress: "Layout Analyzed",
  status: "FAILED",
  log: "Layout analysis failed. Unable to parse design file format.",
  progress_description: "Layout analysis encountered errors",
  history: [
    {
      progress: "project-overview",
      status: "COMPLETED",
      log: "Project overview completed successfully.",
      progress_description: "Project overview completed",
      metadata: []
    },
    {
      progress: "Layout Analyzed",
      status: "IN_PROGRESS",
      log: "Processing layout structure...",
      progress_description: "Analyzing layout structure",
      metadata: []
    }
  ],
  metadata: [
    {
      type: "files",
      data: JSON.stringify([
        {
          "fileName": "error-report.txt",
          "code": "Error: Unsupported file format\nExpected: .fig, .sketch, .xd\nReceived: .unknown"
        }
      ])
    }
  ]
};

export const MOCK_DESIGN_SYSTEM_FAILED: NewPollingResponse = {
  progress: "Design_System Analyzed",
  status: "FAILED",
  log: "Design system analysis failed. No design tokens found in the provided files.",
  progress_description: "Design system analysis failed",
  history: [
    {
      progress: "project-overview",
      status: "COMPLETED",
      log: "Project overview completed successfully.",
      progress_description: "Project overview completed",
      metadata: []
    },
    {
      progress: "Layout Analyzed",
      status: "COMPLETED",
      log: "Layout analysis completed.",
      progress_description: "Layout analysis completed",
      metadata: []
    },
    {
      progress: "Design_System Analyzed",
      status: "IN_PROGRESS",
      log: "Extracting design tokens...",
      progress_description: "Analyzing design system",
      metadata: []
    }
  ],
  metadata: [
    {
      type: "fileNames",
      data: JSON.stringify(["design-system-error.log"])
    }
  ]
};

// Deployed state examples
export const MOCK_DEPLOYED_IN_PROGRESS: NewPollingResponse = {
  progress: "deployed",
  status: "IN_PROGRESS",
  log: "🚀 Building and deploying application to preview environment...\n📦 Compiling TypeScript and bundling assets\n🔧 Optimizing build for production deployment\n🌐 Setting up deployment infrastructure\n⚡ Configuring CDN and performance optimizations\n🔒 Implementing security headers and SSL certificates\n📊 Running final quality checks and performance tests",
  progress_description: "Building production-ready application, optimizing assets, and deploying to secure preview environment with performance monitoring",
  history: [
    {
      progress: "project-overview",
      status: "COMPLETED",
      log: "Project overview completed successfully.",
      progress_description: "Project overview completed",
      metadata: []
    },
    {
      progress: "Layout Analyzed",
      status: "COMPLETED",
      log: "Layout analysis completed.",
      progress_description: "Layout analysis completed",
      metadata: []
    },
    {
      progress: "Design_System Analyzed",
      status: "COMPLETED",
      log: "Design system analysis completed.",
      progress_description: "Design system analysis completed",
      metadata: []
    }
  ],
  metadata: [
    {
      type: "fileNames",
      data: JSON.stringify([
        "build-config.json",
        "deployment-manifest.yml",
        "performance-report.json",
        "security-audit.md"
      ])
    },
    {
      type: "files",
      data: JSON.stringify([
        {
          "fileName": "build/build-config.json",
          "code": "{\n  \"buildVersion\": \"1.0.0\",\n  \"buildTime\": \"2024-01-15T10:30:00Z\",\n  \"environment\": \"production\",\n  \"optimization\": {\n    \"minification\": true,\n    \"treeshaking\": true,\n    \"bundleAnalysis\": true\n  },\n  \"performance\": {\n    \"bundleSize\": \"2.3MB\",\n    \"loadTime\": \"1.2s\",\n    \"lighthouse\": {\n      \"performance\": 95,\n      \"accessibility\": 98,\n      \"bestPractices\": 92,\n      \"seo\": 90\n    }\n  }\n}"
        },
        {
          "fileName": "src/app/app.component.ts",
          "code": "import { Component } from '@angular/core';\nimport { RouterOutlet } from '@angular/router';\nimport { DashboardComponent } from './components/dashboard/dashboard.component';\n\n@Component({\n  selector: 'app-root',\n  standalone: true,\n  imports: [RouterOutlet, DashboardComponent],\n  template: `\n    <div class=\"app-container\">\n      <app-dashboard></app-dashboard>\n      <router-outlet></router-outlet>\n    </div>\n  `,\n  styleUrls: ['./app.component.scss']\n})\nexport class AppComponent {\n  title = 'E-Commerce Dashboard';\n  version = '1.0.0';\n}"
        }
      ])
    }
  ]
};

export const MOCK_DEPLOYED_COMPLETED: NewPollingResponse = {
  progress: "deployed",
  status: "COMPLETED",
  log: "Application successfully deployed and available for preview.",
  progress_description: "Application deployment completed",
  history: [
    {
      progress: "project-overview",
      status: "COMPLETED",
      log: "Project overview completed successfully.",
      progress_description: "Project overview completed",
      metadata: []
    },
    {
      progress: "Layout Analyzed",
      status: "COMPLETED",
      log: "Layout analysis completed.",
      progress_description: "Layout analysis completed",
      metadata: []
    },
    {
      progress: "Design_System Analyzed",
      status: "COMPLETED",
      log: "Design system analysis completed.",
      progress_description: "Design system analysis completed",
      metadata: []
    },
    {
      progress: "deployed",
      status: "IN_PROGRESS",
      log: "Building and deploying application...",
      progress_description: "Deploying application",
      metadata: []
    }
  ],
  metadata: [
    {
      type: "ref_code",
      data: "https://ecommerce-dashboard-2024.preview.augmentcode.com"
    },
    {
      type: "files",
      data: JSON.stringify([
        {
          "fileName": "src/app/app.component.ts",
          "code": "import { Component } from '@angular/core';\nimport { RouterOutlet } from '@angular/router';\nimport { DashboardComponent } from './components/dashboard/dashboard.component';\nimport { HeaderComponent } from './components/header/header.component';\nimport { SidebarComponent } from './components/sidebar/sidebar.component';\nimport { FooterComponent } from './components/footer/footer.component';\n\n@Component({\n  selector: 'app-root',\n  standalone: true,\n  imports: [\n    RouterOutlet,\n    DashboardComponent,\n    HeaderComponent,\n    SidebarComponent,\n    FooterComponent\n  ],\n  template: `\n    <div class=\"app-container\">\n      <app-header></app-header>\n      <div class=\"app-content\">\n        <app-sidebar></app-sidebar>\n        <main class=\"main-content\">\n          <app-dashboard></app-dashboard>\n          <router-outlet></router-outlet>\n        </main>\n      </div>\n      <app-footer></app-footer>\n    </div>\n  `,\n  styleUrls: ['./app.component.scss']\n})\nexport class AppComponent {\n  title = 'E-Commerce Dashboard';\n  version = '1.0.0';\n  \n  constructor() {\n    console.log('E-Commerce Dashboard Application Initialized');\n  }\n}"
        },
        {
          "fileName": "src/app/app.component.scss",
          "code": ".app-container {\n  display: flex;\n  flex-direction: column;\n  min-height: 100vh;\n  font-family: 'Roboto', sans-serif;\n  background-color: #f8f9fa;\n}\n\n.app-content {\n  display: flex;\n  flex: 1;\n  overflow: hidden;\n}\n\n.main-content {\n  flex: 1;\n  padding: 1.5rem;\n  overflow-y: auto;\n  background-color: #ffffff;\n  border-radius: 8px;\n  margin: 1rem;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n@media (max-width: 768px) {\n  .app-content {\n    flex-direction: column;\n  }\n  \n  .main-content {\n    margin: 0.5rem;\n    padding: 1rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .main-content {\n    margin: 0.25rem;\n    padding: 0.75rem;\n    border-radius: 4px;\n  }\n}"
        },
        {
          "fileName": "src/app/components/dashboard/dashboard.component.ts",
          code: "import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Subject, interval } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { AnalyticsWidgetComponent } from '../analytics-widget/analytics-widget.component';\nimport { SalesChartComponent } from '../sales-chart/sales-chart.component';\nimport { InventoryTableComponent } from '../inventory-table/inventory-table.component';\n\ninterface DashboardMetrics {\n  totalSales: number;\n  totalOrders: number;\n  activeUsers: number;\n  conversionRate: number;\n}\n\n@Component({\n  selector: 'app-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    AnalyticsWidgetComponent,\n    SalesChartComponent,\n    InventoryTableComponent\n  ],\n  template: `\n    <div class=\"dashboard-container\">\n      <div class=\"dashboard-header\">\n        <h1>E-Commerce Dashboard</h1>\n        <p class=\"dashboard-subtitle\">Real-time analytics and insights</p>\n      </div>\n      \n      <div class=\"metrics-grid\">\n        <app-analytics-widget\n          title=\"Total Sales\"\n          [value]=\"metrics.totalSales\"\n          format=\"currency\"\n          trend=\"up\"\n          trendValue=\"12.5%\">\n        </app-analytics-widget>\n        \n        <app-analytics-widget\n          title=\"Total Orders\"\n          [value]=\"metrics.totalOrders\"\n          format=\"number\"\n          trend=\"up\"\n          trendValue=\"8.2%\">\n        </app-analytics-widget>\n        \n        <app-analytics-widget\n          title=\"Active Users\"\n          [value]=\"metrics.activeUsers\"\n          format=\"number\"\n          trend=\"down\"\n          trendValue=\"2.1%\">\n        </app-analytics-widget>\n        \n        <app-analytics-widget\n          title=\"Conversion Rate\"\n          [value]=\"metrics.conversionRate\"\n          format=\"percentage\"\n          trend=\"up\"\n          trendValue=\"5.3%\">\n        </app-analytics-widget>\n      </div>\n      \n      <div class=\"charts-section\">\n        <div class=\"chart-container\">\n          <app-sales-chart></app-sales-chart>\n        </div>\n        \n        <div class=\"inventory-container\">\n          <app-inventory-table></app-inventory-table>\n        </div>\n      </div>\n    </div>\n  `,\n  styleUrls: ['./dashboard.component.scss']\n})\nexport class DashboardComponent implements OnInit, OnDestroy {\n  private destroy$ = new Subject<void>();\n  \n  metrics: DashboardMetrics = {\n    totalSales: 125430.50,\n    totalOrders: 1247,\n    activeUsers: 3421,\n    conversionRate: 3.2\n  };\n  \n  ngOnInit(): void {\n    console.log('Dashboard component initialized');\n    this.startRealTimeUpdates();\n  }\n  \n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  \n  private startRealTimeUpdates(): void {\n    // Simulate real-time updates every 30 seconds\n    interval(30000)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(() => {\n        this.updateMetrics();\n      });\n  }\n  \n  private updateMetrics(): void {\n    // Simulate metric updates with random variations\n    this.metrics = {\n      totalSales: this.metrics.totalSales + (Math.random() * 1000 - 500),\n      totalOrders: this.metrics.totalOrders + Math.floor(Math.random() * 10 - 5),\n      activeUsers: this.metrics.activeUsers + Math.floor(Math.random() * 100 - 50),\n      conversionRate: Math.max(0, this.metrics.conversionRate + (Math.random() * 0.2 - 0.1))\n    };\n  }\n}"
        },
        {
          "fileName": "src/app/components/dashboard/dashboard.component.scss",
          code: ".dashboard-container {\n  padding: 0;\n  background-color: transparent;\n}\n\n.dashboard-header {\n  margin-bottom: 2rem;\n  text-align: center;\n  \n  h1 {\n    font-size: 2.5rem;\n    font-weight: 700;\n    color: #2c3e50;\n    margin: 0 0 0.5rem 0;\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    -webkit-background-clip: text;\n    -webkit-text-fill-color: transparent;\n    background-clip: text;\n  }\n  \n  .dashboard-subtitle {\n    font-size: 1.1rem;\n    color: #6c757d;\n    margin: 0;\n    font-weight: 400;\n  }\n}\n\n.metrics-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 1.5rem;\n  margin-bottom: 2rem;\n}\n\n.charts-section {\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 1.5rem;\n  margin-top: 2rem;\n}\n\n.chart-container,\n.inventory-container {\n  background: #ffffff;\n  border-radius: 12px;\n  padding: 1.5rem;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);\n  border: 1px solid #e9ecef;\n}\n\n@media (max-width: 1200px) {\n  .charts-section {\n    grid-template-columns: 1fr;\n  }\n}\n\n@media (max-width: 768px) {\n  .dashboard-header h1 {\n    font-size: 2rem;\n  }\n  \n  .metrics-grid {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n  \n  .chart-container,\n  .inventory-container {\n    padding: 1rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .dashboard-header h1 {\n    font-size: 1.75rem;\n  }\n  \n  .dashboard-header .dashboard-subtitle {\n    font-size: 1rem;\n  }\n}"
        },
        {
          "fileName": "src/app/components/header/header.component.ts",
          code: "import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { SearchBarComponent } from '../search-bar/search-bar.component';\nimport { NotificationPanelComponent } from '../notification-panel/notification-panel.component';\nimport { UserProfileComponent } from '../user-profile/user-profile.component';\n\n@Component({\n  selector: 'app-header',\n  standalone: true,\n  imports: [\n    CommonModule,\n    SearchBarComponent,\n    NotificationPanelComponent,\n    UserProfileComponent\n  ],\n  template: `\n    <header class=\"app-header\">\n      <div class=\"header-content\">\n        <div class=\"logo-section\">\n          <div class=\"logo\">\n            <svg width=\"32\" height=\"32\" viewBox=\"0 0 32 32\" fill=\"none\">\n              <rect width=\"32\" height=\"32\" rx=\"8\" fill=\"#667eea\"/>\n              <path d=\"M8 12h16v8H8z\" fill=\"white\"/>\n              <circle cx=\"16\" cy=\"16\" r=\"2\" fill=\"#667eea\"/>\n            </svg>\n          </div>\n          <h1 class=\"app-title\">E-Commerce Dashboard</h1>\n        </div>\n        \n        <div class=\"header-center\">\n          <app-search-bar></app-search-bar>\n        </div>\n        \n        <div class=\"header-actions\">\n          <app-notification-panel></app-notification-panel>\n          <app-user-profile></app-user-profile>\n        </div>\n      </div>\n    </header>\n  `,\n  styleUrls: ['./header.component.scss']\n})\nexport class HeaderComponent implements OnInit {\n  \n  ngOnInit(): void {\n    console.log('Header component initialized');\n  }\n}"
        },
        {
          "fileName": "src/app/components/header/header.component.scss",
          code: ".app-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  position: sticky;\n  top: 0;\n  z-index: 1000;\n}\n\n.header-content {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 1rem 1.5rem;\n  max-width: 1400px;\n  margin: 0 auto;\n}\n\n.logo-section {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  \n  .logo {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n  }\n  \n  .app-title {\n    font-size: 1.25rem;\n    font-weight: 600;\n    margin: 0;\n    color: white;\n  }\n}\n\n.header-center {\n  flex: 1;\n  max-width: 400px;\n  margin: 0 2rem;\n}\n\n.header-actions {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n\n@media (max-width: 768px) {\n  .header-content {\n    padding: 0.75rem 1rem;\n  }\n  \n  .logo-section .app-title {\n    font-size: 1.1rem;\n  }\n  \n  .header-center {\n    margin: 0 1rem;\n    max-width: 300px;\n  }\n  \n  .header-actions {\n    gap: 0.5rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .header-content {\n    flex-wrap: wrap;\n    gap: 0.5rem;\n  }\n  \n  .header-center {\n    order: 3;\n    flex-basis: 100%;\n    margin: 0.5rem 0 0 0;\n    max-width: none;\n  }\n  \n  .logo-section .app-title {\n    display: none;\n  }\n}"
        },
        {
          "fileName": "src/app/components/sidebar/sidebar.component.ts",
          code: "import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n\ninterface NavigationItem {\n  id: string;\n  label: string;\n  icon: string;\n  route: string;\n  active: boolean;\n  badge?: number;\n}\n\n@Component({\n  selector: 'app-sidebar',\n  standalone: true,\n  imports: [CommonModule, RouterModule],\n  template: `\n    <aside class=\"sidebar\">\n      <nav class=\"sidebar-nav\">\n        <div class=\"nav-section\">\n          <h3 class=\"nav-section-title\">Main</h3>\n          <ul class=\"nav-list\">\n            <li *ngFor=\"let item of mainNavItems\" \n                class=\"nav-item\"\n                [class.active]=\"item.active\">\n              <a [routerLink]=\"item.route\" class=\"nav-link\">\n                <i [class]=\"item.icon\" class=\"nav-icon\"></i>\n                <span class=\"nav-label\">{{ item.label }}</span>\n                <span *ngIf=\"item.badge\" class=\"nav-badge\">{{ item.badge }}</span>\n              </a>\n            </li>\n          </ul>\n        </div>\n        \n        <div class=\"nav-section\">\n          <h3 class=\"nav-section-title\">Analytics</h3>\n          <ul class=\"nav-list\">\n            <li *ngFor=\"let item of analyticsNavItems\" \n                class=\"nav-item\"\n                [class.active]=\"item.active\">\n              <a [routerLink]=\"item.route\" class=\"nav-link\">\n                <i [class]=\"item.icon\" class=\"nav-icon\"></i>\n                <span class=\"nav-label\">{{ item.label }}</span>\n                <span *ngIf=\"item.badge\" class=\"nav-badge\">{{ item.badge }}</span>\n              </a>\n            </li>\n          </ul>\n        </div>\n        \n        <div class=\"nav-section\">\n          <h3 class=\"nav-section-title\">Management</h3>\n          <ul class=\"nav-list\">\n            <li *ngFor=\"let item of managementNavItems\" \n                class=\"nav-item\"\n                [class.active]=\"item.active\">\n              <a [routerLink]=\"item.route\" class=\"nav-link\">\n                <i [class]=\"item.icon\" class=\"nav-icon\"></i>\n                <span class=\"nav-label\">{{ item.label }}</span>\n                <span *ngIf=\"item.badge\" class=\"nav-badge\">{{ item.badge }}</span>\n              </a>\n            </li>\n          </ul>\n        </div>\n      </nav>\n    </aside>\n  `,\n  styleUrls: ['./sidebar.component.scss']\n})\nexport class SidebarComponent implements OnInit {\n  \n  mainNavItems: NavigationItem[] = [\n    { id: 'dashboard', label: 'Dashboard', icon: 'fas fa-tachometer-alt', route: '/dashboard', active: true },\n    { id: 'orders', label: 'Orders', icon: 'fas fa-shopping-cart', route: '/orders', active: false, badge: 12 },\n    { id: 'products', label: 'Products', icon: 'fas fa-box', route: '/products', active: false },\n    { id: 'customers', label: 'Customers', icon: 'fas fa-users', route: '/customers', active: false }\n  ];\n  \n  analyticsNavItems: NavigationItem[] = [\n    { id: 'sales', label: 'Sales Analytics', icon: 'fas fa-chart-line', route: '/analytics/sales', active: false },\n    { id: 'traffic', label: 'Traffic', icon: 'fas fa-chart-bar', route: '/analytics/traffic', active: false },\n    { id: 'conversion', label: 'Conversion', icon: 'fas fa-percentage', route: '/analytics/conversion', active: false }\n  ];\n  \n  managementNavItems: NavigationItem[] = [\n    { id: 'inventory', label: 'Inventory', icon: 'fas fa-warehouse', route: '/inventory', active: false },\n    { id: 'settings', label: 'Settings', icon: 'fas fa-cog', route: '/settings', active: false },\n    { id: 'reports', label: 'Reports', icon: 'fas fa-file-alt', route: '/reports', active: false }\n  ];\n  \n  ngOnInit(): void {\n    console.log('Sidebar component initialized');\n  }\n  \n  onNavItemClick(item: NavigationItem): void {\n    // Reset all items to inactive\n    [...this.mainNavItems, ...this.analyticsNavItems, ...this.managementNavItems]\n      .forEach(navItem => navItem.active = false);\n    \n    // Set clicked item as active\n    item.active = true;\n  }\n}"
        }
      ])
    }
  ]
};

export const MOCK_DEPLOYED_FAILED: NewPollingResponse = {
  progress: "deployed",
  status: "FAILED",
  log: "Deployment failed. Build process encountered compilation errors.",
  progress_description: "Application deployment failed",
  history: [
    {
      progress: "project-overview",
      status: "COMPLETED",
      log: "Project overview completed successfully.",
      progress_description: "Project overview completed",
      metadata: []
    },
    {
      progress: "Layout Analyzed",
      status: "COMPLETED",
      log: "Layout analysis completed.",
      progress_description: "Layout analysis completed",
      metadata: []
    },
    {
      progress: "Design_System Analyzed",
      status: "COMPLETED",
      log: "Design system analysis completed.",
      progress_description: "Design system analysis completed",
      metadata: []
    },
    {
      progress: "deployed",
      status: "IN_PROGRESS",
      log: "Building and deploying application...",
      progress_description: "Deploying application",
      metadata: []
    }
  ],
  metadata: [
    {
      type: "files",
      data: JSON.stringify([
        {
          "fileName": "build-error.log",
          "code": "ERROR: Compilation failed\nTypeScript error in src/app/app.component.ts:5:10\nProperty 'invalidProperty' does not exist on type 'AppComponent'"
        }
      ])
    },
    {
      type: "fileNames",
      data: JSON.stringify(["build-error.log", "deployment-failure.txt"])
    }
  ]
};
